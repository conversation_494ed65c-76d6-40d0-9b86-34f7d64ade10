﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.Xml</name>
  </assembly>
  <members>
    <member name="T:System.Security.Cryptography.Xml.CipherData">
      <summary>Represents the <see langword="&lt;CipherData&gt;" /> element in XML encryption. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.CipherData.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.CipherData" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.CipherData.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.CipherData" /> class using a byte array as the <see cref="P:System.Security.Cryptography.Xml.CipherData.CipherValue" /> value.</summary>
      <param name="cipherValue">The encrypted data to use for the <see langword="&lt;CipherValue&gt;" /> element.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="cipherValue" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.Xml.CipherData.CipherValue" /> property has already been set.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.CipherData.#ctor(System.Security.Cryptography.Xml.CipherReference)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.CipherData" /> class using a <see cref="T:System.Security.Cryptography.Xml.CipherReference" /> object.</summary>
      <param name="cipherReference">The <see cref="T:System.Security.Cryptography.Xml.CipherReference" /> object to use.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="cipherValue" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.Xml.CipherData.CipherValue" /> property has already been set.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.CipherData.GetXml">
      <summary>Gets the XML values for the <see cref="T:System.Security.Cryptography.Xml.CipherData" /> object.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.Xml.CipherData.CipherValue" /> property and the <see cref="P:System.Security.Cryptography.Xml.CipherData.CipherReference" /> property are <see langword="null" />.</exception>
      <returns>A <see cref="T:System.Xml.XmlElement" /> object that represents the XML information for the <see cref="T:System.Security.Cryptography.Xml.CipherData" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.CipherData.LoadXml(System.Xml.XmlElement)">
      <summary>Loads XML data from an <see cref="T:System.Xml.XmlElement" /> into a <see cref="T:System.Security.Cryptography.Xml.CipherData" /> object.</summary>
      <param name="value">An <see cref="T:System.Xml.XmlElement" /> that represents the XML data to load.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.Xml.CipherData.CipherValue" /> property and the <see cref="P:System.Security.Cryptography.Xml.CipherData.CipherReference" /> property are <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.CipherData.CipherReference">
      <summary>Gets or sets the <see langword="&lt;CipherReference&gt;" /> element.</summary>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Security.Cryptography.Xml.CipherData.CipherReference" /> property was set to <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.Xml.CipherData.CipherReference" /> property was set more than once.</exception>
      <returns>A <see cref="T:System.Security.Cryptography.Xml.CipherReference" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.CipherData.CipherValue">
      <summary>Gets or sets the <see langword="&lt;CipherValue&gt;" /> element.</summary>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Security.Cryptography.Xml.CipherData.CipherValue" /> property was set to <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.Xml.CipherData.CipherValue" /> property was set more than once.</exception>
      <returns>A byte array that represents the <see langword="&lt;CipherValue&gt;" /> element.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.CipherReference">
      <summary>Represents the <see langword="&lt;CipherReference&gt;" /> element in XML encryption. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.CipherReference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.CipherReference" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.CipherReference.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.CipherReference" /> class using the specified Uniform Resource Identifier (URI).</summary>
      <param name="uri">A Uniform Resource Identifier (URI) pointing to the encrypted data.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.CipherReference.#ctor(System.String,System.Security.Cryptography.Xml.TransformChain)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.CipherReference" /> class using the specified Uniform Resource Identifier (URI) and transform chain information.</summary>
      <param name="uri">A Uniform Resource Identifier (URI) pointing to the encrypted data.</param>
      <param name="transformChain">A <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> object that describes transforms to do on the encrypted data.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.CipherReference.GetXml">
      <summary>Returns the XML representation of a <see cref="T:System.Security.Cryptography.Xml.CipherReference" /> object.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="T:System.Security.Cryptography.Xml.CipherReference" /> value is <see langword="null" />.</exception>
      <returns>An <see cref="T:System.Xml.XmlElement" /> that represents the <see langword="&lt;CipherReference&gt;" /> element in XML encryption.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.CipherReference.LoadXml(System.Xml.XmlElement)">
      <summary>Loads XML information into the <see langword="&lt;CipherReference&gt;" /> element in XML encryption.</summary>
      <param name="value">An <see cref="T:System.Xml.XmlElement" /> object that represents an XML element to use as the reference.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> provided is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Security.Cryptography.Xml.CryptoSignedXmlRecursionException">
      <summary>The exception that's thrown when the signed XML recursion limit is reached.
            Catch this exception when computing the hash, in addition to the other exceptions that ComputeHash can throw.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.CryptoSignedXmlRecursionException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.CryptoSignedXmlRecursionException" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.CryptoSignedXmlRecursionException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.CryptoSignedXmlRecursionException" /> class with serialized data.</summary>
      <param name="info">The object that holds the serialized object data.</param>
      <param name="context">The contextual information about the source or destination.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.CryptoSignedXmlRecursionException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.CryptoSignedXmlRecursionException" /> class with a specified error message.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.CryptoSignedXmlRecursionException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.CryptoSignedXmlRecursionException" /> class with a specified error message and a reference to the inner exception that is the cause of this exception.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <param name="inner">The exception that is the cause of the current exception.</param>
    </member>
    <member name="T:System.Security.Cryptography.Xml.DataObject">
      <summary>Represents the object element of an XML signature that holds data to be signed.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.DataObject.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.DataObject" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.DataObject.#ctor(System.String,System.String,System.String,System.Xml.XmlElement)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.DataObject" /> class with the specified identification, MIME type, encoding, and data.</summary>
      <param name="id">The identification to initialize the new instance of <see cref="T:System.Security.Cryptography.Xml.DataObject" /> with.</param>
      <param name="mimeType">The MIME type of the data used to initialize the new instance of <see cref="T:System.Security.Cryptography.Xml.DataObject" />.</param>
      <param name="encoding">The encoding of the data used to initialize the new instance of <see cref="T:System.Security.Cryptography.Xml.DataObject" />.</param>
      <param name="data">The data to initialize the new instance of <see cref="T:System.Security.Cryptography.Xml.DataObject" /> with.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="data" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.DataObject.GetXml">
      <summary>Returns the XML representation of the <see cref="T:System.Security.Cryptography.Xml.DataObject" /> object.</summary>
      <returns>The XML representation of the <see cref="T:System.Security.Cryptography.Xml.DataObject" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.DataObject.LoadXml(System.Xml.XmlElement)">
      <summary>Loads a <see cref="T:System.Security.Cryptography.Xml.DataObject" /> state from an XML element.</summary>
      <param name="value">The XML element to load the <see cref="T:System.Security.Cryptography.Xml.DataObject" /> state from.</param>
      <exception cref="T:System.ArgumentNullException">The value from the XML element is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.DataObject.Data">
      <summary>Gets or sets the data value of the current <see cref="T:System.Security.Cryptography.Xml.DataObject" /> object.</summary>
      <exception cref="T:System.ArgumentNullException">The value used to set the property is <see langword="null" />.</exception>
      <returns>The data of the current <see cref="T:System.Security.Cryptography.Xml.DataObject" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.DataObject.Encoding">
      <summary>Gets or sets the encoding of the current <see cref="T:System.Security.Cryptography.Xml.DataObject" /> object.</summary>
      <returns>The type of encoding of the current <see cref="T:System.Security.Cryptography.Xml.DataObject" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.DataObject.Id">
      <summary>Gets or sets the identification of the current <see cref="T:System.Security.Cryptography.Xml.DataObject" /> object.</summary>
      <returns>The name of the element that contains data to be used.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.DataObject.MimeType">
      <summary>Gets or sets the MIME type of the current <see cref="T:System.Security.Cryptography.Xml.DataObject" /> object.</summary>
      <returns>The MIME type of the current <see cref="T:System.Security.Cryptography.Xml.DataObject" /> object. The default is <see langword="null" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.DataReference">
      <summary>Represents the <see langword="&lt;DataReference&gt;" /> element used in XML encryption. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.DataReference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.DataReference" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.DataReference.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.DataReference" /> class using the specified Uniform Resource Identifier (URI).</summary>
      <param name="uri">A Uniform Resource Identifier (URI) that points to the encrypted data.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.DataReference.#ctor(System.String,System.Security.Cryptography.Xml.TransformChain)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.DataReference" /> class using the specified Uniform Resource Identifier (URI) and a <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> object.</summary>
      <param name="uri">A Uniform Resource Identifier (URI) that points to the encrypted data.</param>
      <param name="transformChain">A <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> object that describes transforms to do on the encrypted data.</param>
    </member>
    <member name="T:System.Security.Cryptography.Xml.DSAKeyValue">
      <summary>Represents the <see cref="T:System.Security.Cryptography.DSA" /> private key of the <see langword="&lt;KeyInfo&gt;" /> element.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.DSAKeyValue.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.DSAKeyValue" /> class with a new, randomly-generated <see cref="T:System.Security.Cryptography.DSA" /> public key.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.DSAKeyValue.#ctor(System.Security.Cryptography.DSA)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.DSAKeyValue" /> class with the specified <see cref="T:System.Security.Cryptography.DSA" /> public key.</summary>
      <param name="key">The instance of an implementation of the <see cref="T:System.Security.Cryptography.DSA" /> class that holds the public key.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.DSAKeyValue.GetXml">
      <summary>Returns the XML representation of a <see cref="T:System.Security.Cryptography.Xml.DSAKeyValue" /> element.</summary>
      <returns>The XML representation of the <see cref="T:System.Security.Cryptography.Xml.DSAKeyValue" /> element.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.DSAKeyValue.LoadXml(System.Xml.XmlElement)">
      <summary>Loads a <see cref="T:System.Security.Cryptography.Xml.DSAKeyValue" /> state from an XML element.</summary>
      <param name="value">The XML element to load the <see cref="T:System.Security.Cryptography.Xml.DSAKeyValue" /> state from.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="value" /> parameter is not a valid <see cref="T:System.Security.Cryptography.Xml.DSAKeyValue" /> XML element.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.DSAKeyValue.Key">
      <summary>Gets or sets the key value represented by a <see cref="T:System.Security.Cryptography.DSA" /> object.</summary>
      <returns>The public key represented by a <see cref="T:System.Security.Cryptography.DSA" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.EncryptedData">
      <summary>Represents the <see langword="&lt;EncryptedData&gt;" /> element in XML encryption. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedData.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptedData" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedData.GetXml">
      <summary>Returns the XML representation of the <see cref="T:System.Security.Cryptography.Xml.EncryptedData" /> object.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="T:System.Security.Cryptography.Xml.EncryptedData" /> value is <see langword="null" />.</exception>
      <returns>An <see cref="T:System.Xml.XmlElement" /> that represents the <see langword="&lt;EncryptedData&gt;" /> element in XML encryption.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedData.LoadXml(System.Xml.XmlElement)">
      <summary>Loads XML information into the <see langword="&lt;EncryptedData&gt;" /> element in XML encryption.</summary>
      <param name="value">An <see cref="T:System.Xml.XmlElement" /> object representing an XML element to use for the <see langword="&lt;EncryptedData&gt;" /> element.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> provided is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="value" /> parameter does not contain a &lt;<see langword="CypherData" />&gt; node.</exception>
    </member>
    <member name="T:System.Security.Cryptography.Xml.EncryptedKey">
      <summary>Represents the <see langword="&lt;EncryptedKey&gt;" /> element in XML encryption. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedKey.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptedKey" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedKey.AddReference(System.Security.Cryptography.Xml.DataReference)">
      <summary>Adds a <see langword="&lt;DataReference&gt;" /> element to the <see langword="&lt;ReferenceList&gt;" /> element.</summary>
      <param name="dataReference">A <see cref="T:System.Security.Cryptography.Xml.DataReference" /> object to add to the <see cref="P:System.Security.Cryptography.Xml.EncryptedKey.ReferenceList" /> property.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedKey.AddReference(System.Security.Cryptography.Xml.KeyReference)">
      <summary>Adds a <see langword="&lt;KeyReference&gt;" /> element to the <see langword="&lt;ReferenceList&gt;" /> element.</summary>
      <param name="keyReference">A <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object to add to the <see cref="P:System.Security.Cryptography.Xml.EncryptedKey.ReferenceList" /> property.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedKey.GetXml">
      <summary>Returns the XML representation of the <see cref="T:System.Security.Cryptography.Xml.EncryptedKey" /> object.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="T:System.Security.Cryptography.Xml.EncryptedKey" /> value is <see langword="null" />.</exception>
      <returns>An <see cref="T:System.Xml.XmlElement" /> that represents the <see langword="&lt;EncryptedKey&gt;" /> element in XML encryption.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedKey.LoadXml(System.Xml.XmlElement)">
      <summary>Loads the specified XML information into the <see langword="&lt;EncryptedKey&gt;" /> element in XML encryption.</summary>
      <param name="value">An <see cref="T:System.Xml.XmlElement" /> representing an XML element to use for the <see langword="&lt;EncryptedKey&gt;" /> element.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="value" /> parameter does not contain a <see cref="T:System.Security.Cryptography.Xml.CipherData" /> element.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedKey.CarriedKeyName">
      <summary>Gets or sets the optional <see langword="&lt;CarriedKeyName&gt;" /> element in XML encryption.</summary>
      <returns>A string that represents a name for the key value.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedKey.Recipient">
      <summary>Gets or sets the optional <see langword="Recipient" /> attribute in XML encryption.</summary>
      <returns>A string representing the value of the <see langword="Recipient" /> attribute.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedKey.ReferenceList">
      <summary>Gets or sets the <see langword="&lt;ReferenceList&gt;" /> element in XML encryption.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.EncryptedReference">
      <summary>Represents the abstract base class used in XML encryption from which the <see cref="T:System.Security.Cryptography.Xml.CipherReference" />, <see cref="T:System.Security.Cryptography.Xml.KeyReference" />, and <see cref="T:System.Security.Cryptography.Xml.DataReference" /> classes derive.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedReference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptedReference" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedReference.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptedReference" /> class using the specified Uniform Resource Identifier (URI).</summary>
      <param name="uri">The Uniform Resource Identifier (URI) that points to the data to encrypt.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="uri" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedReference.#ctor(System.String,System.Security.Cryptography.Xml.TransformChain)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptedReference" /> class using the specified Uniform Resource Identifier (URI) and transform chain.</summary>
      <param name="uri">The Uniform Resource Identifier (URI) that points to the data to encrypt.</param>
      <param name="transformChain">A <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> object that describes transforms to be done on the data to encrypt.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="uri" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedReference.AddTransform(System.Security.Cryptography.Xml.Transform)">
      <summary>Adds a <see cref="T:System.Security.Cryptography.Xml.Transform" /> object to the current transform chain of an <see cref="T:System.Security.Cryptography.Xml.EncryptedReference" /> object.</summary>
      <param name="transform">A <see cref="T:System.Security.Cryptography.Xml.Transform" /> object to add to the transform chain.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedReference.GetXml">
      <summary>Returns the XML representation of an <see cref="T:System.Security.Cryptography.Xml.EncryptedReference" /> object.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.Xml.EncryptedReference.ReferenceType" /> property is <see langword="null" />.</exception>
      <returns>An <see cref="T:System.Xml.XmlElement" /> object that represents the values of the <see langword="&lt;EncryptedReference&gt;" /> element in XML encryption.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedReference.LoadXml(System.Xml.XmlElement)">
      <summary>Loads an XML element into an <see cref="T:System.Security.Cryptography.Xml.EncryptedReference" /> object.</summary>
      <param name="value">An <see cref="T:System.Xml.XmlElement" /> object that represents an XML element.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedReference.CacheValid">
      <summary>Gets a value that indicates whether the cache is valid.</summary>
      <returns>
        <see langword="true" /> if the cache is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedReference.ReferenceType">
      <summary>Gets or sets a reference type.</summary>
      <returns>The reference type of the encrypted data.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedReference.TransformChain">
      <summary>Gets or sets the transform chain of an <see cref="T:System.Security.Cryptography.Xml.EncryptedReference" /> object.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> object that describes transforms used on the encrypted data.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedReference.Uri">
      <summary>Gets or sets the Uniform Resource Identifier (URI) of an <see cref="T:System.Security.Cryptography.Xml.EncryptedReference" /> object.</summary>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Security.Cryptography.Xml.EncryptedReference.Uri" /> property was set to <see langword="null" />.</exception>
      <returns>The Uniform Resource Identifier (URI) of the <see cref="T:System.Security.Cryptography.Xml.EncryptedReference" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.EncryptedType">
      <summary>Represents the abstract base class from which the classes <see cref="T:System.Security.Cryptography.Xml.EncryptedData" /> and <see cref="T:System.Security.Cryptography.Xml.EncryptedKey" /> derive.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedType.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptedType" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedType.AddProperty(System.Security.Cryptography.Xml.EncryptionProperty)">
      <summary>Adds an <see langword="&lt;EncryptionProperty&gt;" /> child element to the <see langword="&lt;EncryptedProperties&gt;" /> element in the current <see cref="T:System.Security.Cryptography.Xml.EncryptedType" /> object in XML encryption.</summary>
      <param name="ep">An <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedType.GetXml">
      <summary>Returns the XML representation of the <see cref="T:System.Security.Cryptography.Xml.EncryptedType" /> object.</summary>
      <returns>An <see cref="T:System.Xml.XmlElement" /> object that represents the <see langword="&lt;EncryptedType&gt;" /> element in XML encryption.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedType.LoadXml(System.Xml.XmlElement)">
      <summary>Loads XML information into the <see langword="&lt;EncryptedType&gt;" /> element in XML encryption.</summary>
      <param name="value">An <see cref="T:System.Xml.XmlElement" /> object representing an XML element to use in the <see langword="&lt;EncryptedType&gt;" /> element.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> provided is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedType.CipherData">
      <summary>Gets or sets the <see cref="T:System.Security.Cryptography.Xml.CipherData" /> value for an instance of an <see cref="T:System.Security.Cryptography.Xml.EncryptedType" /> class.</summary>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Security.Cryptography.Xml.EncryptedType.CipherData" /> property was set to <see langword="null" />.</exception>
      <returns>A <see cref="T:System.Security.Cryptography.Xml.CipherData" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedType.Encoding">
      <summary>Gets or sets the <see langword="Encoding" /> attribute of an <see cref="T:System.Security.Cryptography.Xml.EncryptedType" /> instance in XML encryption.</summary>
      <returns>A string that describes the encoding of the encrypted data.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedType.EncryptionMethod">
      <summary>Gets or sets the <see langword="&lt;EncryptionMethod&gt;" /> element for XML encryption.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.Xml.EncryptionMethod" /> object that represents the <see langword="&lt;EncryptionMethod&gt;" /> element.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedType.EncryptionProperties">
      <summary>Gets or sets the <see langword="&lt;EncryptionProperties&gt;" /> element in XML encryption.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedType.Id">
      <summary>Gets or sets the <see langword="Id" /> attribute of an <see cref="T:System.Security.Cryptography.Xml.EncryptedType" /> instance in XML encryption.</summary>
      <returns>A string of the <see langword="Id" /> attribute of the <see langword="&lt;EncryptedType&gt;" /> element.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedType.KeyInfo">
      <summary>Gets of sets the <see langword="&lt;KeyInfo&gt;" /> element in XML encryption.</summary>
      <returns>A <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedType.MimeType">
      <summary>Gets or sets the <see langword="MimeType" /> attribute of an <see cref="T:System.Security.Cryptography.Xml.EncryptedType" /> instance in XML encryption.</summary>
      <returns>A string that describes the media type of the encrypted data.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedType.Type">
      <summary>Gets or sets the <see langword="Type" /> attribute of an <see cref="T:System.Security.Cryptography.Xml.EncryptedType" /> instance in XML encryption.</summary>
      <returns>A string that describes the text form of the encrypted data.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.EncryptedXml">
      <summary>Represents the process model for implementing XML encryption.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncAES128KeyWrapUrl">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for the 128-bit Advanced Encryption Standard (AES) Key Wrap algorithm (also known as the Rijndael Key Wrap algorithm). This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncAES128Url">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for the 128-bit Advanced Encryption Standard (AES) algorithm (also known as the Rijndael algorithm). This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncAES192KeyWrapUrl">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for the 192-bit Advanced Encryption Standard (AES) Key Wrap algorithm (also known as the Rijndael Key Wrap algorithm). This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncAES192Url">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for the 192-bit Advanced Encryption Standard (AES) algorithm (also known as the Rijndael algorithm). This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncAES256KeyWrapUrl">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for the 256-bit Advanced Encryption Standard (AES) Key Wrap algorithm (also known as the Rijndael Key Wrap algorithm). This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncAES256Url">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for the 256-bit Advanced Encryption Standard (AES) algorithm (also known as the Rijndael algorithm). This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncDESUrl">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for the Digital Encryption Standard (DES) algorithm. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncElementContentUrl">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for XML encryption element content. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncElementUrl">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for an XML encryption element. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncEncryptedKeyUrl">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for the XML encryption <see langword="&lt;EncryptedKey&gt;" /> element. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncNamespaceUrl">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for XML encryption syntax and processing. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncRSA15Url">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for the RSA Public Key Cryptography Standard (PKCS) Version 1.5 algorithm. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncRSAOAEPUrl">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for the RSA Optimal Asymmetric Encryption Padding (OAEP) encryption algorithm. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncSHA256Url">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for the SHA-256 algorithm. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncSHA512Url">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for the SHA-512 algorithm. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncTripleDESKeyWrapUrl">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for the TRIPLEDES key wrap algorithm. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.EncryptedXml.XmlEncTripleDESUrl">
      <summary>Represents the namespace Uniform Resource Identifier (URI) for the Triple DES algorithm. This field is constant.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptedXml" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.#ctor(System.Xml.XmlDocument)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptedXml" /> class using the specified XML document.</summary>
      <param name="document">An <see cref="T:System.Xml.XmlDocument" /> object used to initialize the <see cref="T:System.Security.Cryptography.Xml.EncryptedXml" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.#ctor(System.Xml.XmlDocument,System.Security.Policy.Evidence)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptedXml" /> class using the specified XML document and evidence.</summary>
      <param name="document">An <see cref="T:System.Xml.XmlDocument" /> object used to initialize the <see cref="T:System.Security.Cryptography.Xml.EncryptedXml" /> object.</param>
      <param name="evidence">An <see cref="T:System.Security.Policy.Evidence" /> object associated with the <see cref="T:System.Xml.XmlDocument" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.AddKeyNameMapping(System.String,System.Object)">
      <summary>Defines a mapping between a key name and a symmetric key or an asymmetric key.</summary>
      <param name="keyName">The name to map to <paramref name="keyObject" />.</param>
      <param name="keyObject">The symmetric key to map to <paramref name="keyName" />.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="keyName" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The value of the <paramref name="keyObject" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The value of the <paramref name="keyObject" /> parameter is not an RSA algorithm or a symmetric key.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.ClearKeyNameMappings">
      <summary>Resets all key name mapping.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.DecryptData(System.Security.Cryptography.Xml.EncryptedData,System.Security.Cryptography.SymmetricAlgorithm)">
      <summary>Decrypts an <see langword="&lt;EncryptedData&gt;" /> element using the specified symmetric algorithm.</summary>
      <param name="encryptedData">The data to decrypt.</param>
      <param name="symmetricAlgorithm">The symmetric key used to decrypt <paramref name="encryptedData" />.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="encryptedData" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The value of the <paramref name="symmetricAlgorithm" /> parameter is <see langword="null" />.</exception>
      <returns>A byte array that contains the raw decrypted plain text.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.DecryptDocument">
      <summary>Decrypts all <see langword="&lt;EncryptedData&gt;" /> elements of the XML document that were specified during initialization of the <see cref="T:System.Security.Cryptography.Xml.EncryptedXml" /> class.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The cryptographic key used to decrypt the document was not found.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.DecryptEncryptedKey(System.Security.Cryptography.Xml.EncryptedKey)">
      <summary>Determines the key represented by the <see cref="T:System.Security.Cryptography.Xml.EncryptedKey" /> element.</summary>
      <param name="encryptedKey">The <see cref="T:System.Security.Cryptography.Xml.EncryptedKey" /> object that contains the key to retrieve.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="encryptedKey" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The value of the <paramref name="encryptedKey" /> parameter is not the Triple DES Key Wrap algorithm or the Advanced Encryption Standard (AES) Key Wrap algorithm (also called Rijndael).</exception>
      <returns>A byte array that contains the key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.DecryptKey(System.Byte[],System.Security.Cryptography.RSA,System.Boolean)">
      <summary>Decrypts an <see langword="&lt;EncryptedKey&gt;" /> element using an asymmetric algorithm.</summary>
      <param name="keyData">An array of bytes that represents an encrypted <see langword="&lt;EncryptedKey&gt;" /> element.</param>
      <param name="rsa">The asymmetric key used to decrypt <paramref name="keyData" />.</param>
      <param name="useOAEP">A value that specifies whether to use Optimal Asymmetric Encryption Padding (OAEP).</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="keyData" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The value of the <paramref name="rsa" /> parameter is <see langword="null" />.</exception>
      <returns>A byte array that contains the plain text key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.DecryptKey(System.Byte[],System.Security.Cryptography.SymmetricAlgorithm)">
      <summary>Decrypts an <see langword="&lt;EncryptedKey&gt;" /> element using a symmetric algorithm.</summary>
      <param name="keyData">An array of bytes that represents an encrypted <see langword="&lt;EncryptedKey&gt;" /> element.</param>
      <param name="symmetricAlgorithm">The symmetric key used to decrypt <paramref name="keyData" />.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="keyData" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The value of the <paramref name="symmetricAlgorithm" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The value of the <paramref name="symmetricAlgorithm" /> element is not the Triple DES Key Wrap algorithm or the Advanced Encryption Standard (AES) Key Wrap algorithm (also called Rijndael).</exception>
      <returns>A byte array that contains the plain text key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.Encrypt(System.Xml.XmlElement,System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Encrypts the outer XML of an element using the specified X.509 certificate.</summary>
      <param name="inputElement">The XML element to encrypt.</param>
      <param name="certificate">The X.509 certificate to use for encryption.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="inputElement" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The value of the <paramref name="certificate" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The value of the <paramref name="certificate" /> parameter does not represent an RSA key algorithm.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.Xml.EncryptedData" /> element that represents the encrypted XML data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.Encrypt(System.Xml.XmlElement,System.String)">
      <summary>Encrypts the outer XML of an element using the specified key in the key mapping table.</summary>
      <param name="inputElement">The XML element to encrypt.</param>
      <param name="keyName">A key name that can be found in the key mapping table.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="inputElement" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The value of the <paramref name="keyName" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The value of the <paramref name="keyName" /> parameter does not match a registered key name pair.  
  
 -or-  
  
 The cryptographic key described by the <paramref name="keyName" /> parameter is not supported.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.Xml.EncryptedData" /> object that represents the encrypted XML data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.EncryptData(System.Byte[],System.Security.Cryptography.SymmetricAlgorithm)">
      <summary>Encrypts data in the specified byte array using the specified symmetric algorithm.</summary>
      <param name="plaintext">The data to encrypt.</param>
      <param name="symmetricAlgorithm">The symmetric algorithm to use for encryption.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="plaintext" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The value of the <paramref name="symmetricAlgorithm" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The data could not be encrypted using the specified parameters.</exception>
      <returns>A byte array of encrypted data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.EncryptData(System.Xml.XmlElement,System.Security.Cryptography.SymmetricAlgorithm,System.Boolean)">
      <summary>Encrypts the specified element or its contents using the specified symmetric algorithm.</summary>
      <param name="inputElement">The element or its contents to encrypt.</param>
      <param name="symmetricAlgorithm">The symmetric algorithm to use for encryption.</param>
      <param name="content">
        <see langword="true" /> to encrypt only the contents of the element; <see langword="false" /> to encrypt the entire element.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="inputElement" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The value of the <paramref name="symmetricAlgorithm" /> parameter is <see langword="null" />.</exception>
      <returns>A byte array that contains the encrypted data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.EncryptKey(System.Byte[],System.Security.Cryptography.RSA,System.Boolean)">
      <summary>Encrypts the key that a recipient uses to decrypt an <see langword="&lt;EncryptedData&gt;" /> element.</summary>
      <param name="keyData">The key to encrypt.</param>
      <param name="rsa">The asymmetric key used to encrypt <paramref name="keyData" />.</param>
      <param name="useOAEP">A value that specifies whether to use Optimal Asymmetric Encryption Padding (OAEP).</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="keyData" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The value of the <paramref name="rsa" /> parameter is <see langword="null" />.</exception>
      <returns>A byte array that represents the encrypted value of the <paramref name="keyData" /> parameter.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.EncryptKey(System.Byte[],System.Security.Cryptography.SymmetricAlgorithm)">
      <summary>Encrypts a key using a symmetric algorithm that a recipient uses to decrypt an <see langword="&lt;EncryptedData&gt;" /> element.</summary>
      <param name="keyData">The key to encrypt.</param>
      <param name="symmetricAlgorithm">The symmetric key used to encrypt <paramref name="keyData" />.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="keyData" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The value of the <paramref name="symmetricAlgorithm" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The value of the <paramref name="symmetricAlgorithm" /> parameter is not the Triple DES Key Wrap algorithm or the Advanced Encryption Standard (AES) Key Wrap algorithm (also called Rijndael).</exception>
      <returns>A byte array that represents the encrypted value of the <paramref name="keyData" /> parameter.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.GetDecryptionIV(System.Security.Cryptography.Xml.EncryptedData,System.String)">
      <summary>Retrieves the decryption initialization vector (IV) from an <see cref="T:System.Security.Cryptography.Xml.EncryptedData" /> object.</summary>
      <param name="encryptedData">The <see cref="T:System.Security.Cryptography.Xml.EncryptedData" /> object that contains the initialization vector (IV) to retrieve.</param>
      <param name="symmetricAlgorithmUri">The Uniform Resource Identifier (URI) that describes the cryptographic algorithm associated with the <paramref name="encryptedData" /> value.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="encryptedData" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The value of the <paramref name="encryptedData" /> parameter has an <see cref="P:System.Security.Cryptography.Xml.EncryptedType.EncryptionMethod" /> property that is null.  
  
 -or-  
  
 The value of the <paramref name="symmetricAlgorithmUrisymAlgUri" /> parameter is not a supported algorithm.</exception>
      <returns>A byte array that contains the decryption initialization vector (IV).</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.GetDecryptionKey(System.Security.Cryptography.Xml.EncryptedData,System.String)">
      <summary>Retrieves the decryption key from the specified <see cref="T:System.Security.Cryptography.Xml.EncryptedData" /> object.</summary>
      <param name="encryptedData">The <see cref="T:System.Security.Cryptography.Xml.EncryptedData" /> object that contains the decryption key to retrieve.</param>
      <param name="symmetricAlgorithmUri">The size of the decryption key to retrieve.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="encryptedData" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The encryptedData parameter has an <see cref="P:System.Security.Cryptography.Xml.EncryptedType.EncryptionMethod" /> property that is null.  
  
 -or-  
  
 The encrypted key cannot be retrieved using the specified parameters.</exception>
      <returns>A <see cref="T:System.Security.Cryptography.SymmetricAlgorithm" /> object associated with the decryption key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.GetIdElement(System.Xml.XmlDocument,System.String)">
      <summary>Determines how to resolve internal Uniform Resource Identifier (URI) references.</summary>
      <param name="document">An <see cref="T:System.Xml.XmlDocument" /> object that contains an element with an ID value.</param>
      <param name="idValue">A string that represents the ID value.</param>
      <returns>An <see cref="T:System.Xml.XmlElement" /> object that contains an ID indicating how internal Uniform Resource Identifiers (URIs) are to be resolved.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.ReplaceData(System.Xml.XmlElement,System.Byte[])">
      <summary>Replaces an <see langword="&lt;EncryptedData&gt;" /> element with a specified decrypted sequence of bytes.</summary>
      <param name="inputElement">The <see langword="&lt;EncryptedData&gt;" /> element to replace.</param>
      <param name="decryptedData">The decrypted data to replace <paramref name="inputElement" /> with.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="inputElement" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The value of the <paramref name="decryptedData" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptedXml.ReplaceElement(System.Xml.XmlElement,System.Security.Cryptography.Xml.EncryptedData,System.Boolean)">
      <summary>Replaces the specified element with the specified <see cref="T:System.Security.Cryptography.Xml.EncryptedData" /> object.</summary>
      <param name="inputElement">The element to replace with an <see langword="&lt;EncryptedData&gt;" /> element.</param>
      <param name="encryptedData">The <see cref="T:System.Security.Cryptography.Xml.EncryptedData" /> object to replace the <paramref name="inputElement" /> parameter with.</param>
      <param name="content">
        <see langword="true" /> to replace only the contents of the element; <see langword="false" /> to replace the entire element.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="inputElement" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The value of the <paramref name="encryptedData" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedXml.DocumentEvidence">
      <summary>Gets or sets the evidence of the <see cref="T:System.Xml.XmlDocument" /> object from which the <see cref="T:System.Security.Cryptography.Xml.EncryptedXml" /> object is constructed.</summary>
      <returns>An <see cref="T:System.Security.Policy.Evidence" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedXml.Encoding">
      <summary>Gets or sets the encoding used for XML encryption.</summary>
      <returns>An <see cref="T:System.Text.Encoding" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedXml.Mode">
      <summary>Gets or sets the cipher mode used for XML encryption.</summary>
      <returns>One of the <see cref="T:System.Security.Cryptography.CipherMode" /> values.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedXml.Padding">
      <summary>Gets or sets the padding mode used for XML encryption.</summary>
      <returns>One of the <see cref="T:System.Security.Cryptography.PaddingMode" /> values that specifies the type of padding used for encryption.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedXml.Recipient">
      <summary>Gets or sets the recipient of the encrypted key information.</summary>
      <returns>The recipient of the encrypted key information.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedXml.Resolver">
      <summary>Gets or sets the <see cref="T:System.Xml.XmlResolver" /> object used by the Document Object Model (DOM) to resolve external XML references.</summary>
      <returns>An <see cref="T:System.Xml.XmlResolver" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptedXml.XmlDSigSearchDepth">
      <summary>Gets or sets the XML digital signature recursion depth to prevent infinite recursion and stack overflow. This might happen if the digital signature XML contains the URI which then points back to the original XML.</summary>
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.EncryptionMethod">
      <summary>Encapsulates the encryption algorithm used for XML encryption.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionMethod.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptionMethod" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionMethod.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptionMethod" /> class specifying an algorithm Uniform Resource Identifier (URI).</summary>
      <param name="algorithm">The Uniform Resource Identifier (URI) that describes the algorithm represented by an instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptionMethod" /> class.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionMethod.GetXml">
      <summary>Returns an <see cref="T:System.Xml.XmlElement" /> object that encapsulates an instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptionMethod" /> class.</summary>
      <returns>An <see cref="T:System.Xml.XmlElement" /> object that encapsulates an instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptionMethod" /> class.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionMethod.LoadXml(System.Xml.XmlElement)">
      <summary>Parses the specified <see cref="T:System.Xml.XmlElement" /> object and configures the internal state of the <see cref="T:System.Security.Cryptography.Xml.EncryptionMethod" /> object to match.</summary>
      <param name="value">An <see cref="T:System.Xml.XmlElement" /> object to parse.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The key size expressed in the <paramref name="value" /> parameter was less than 0.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptionMethod.KeyAlgorithm">
      <summary>Gets or sets a Uniform Resource Identifier (URI) that describes the algorithm to use for XML encryption.</summary>
      <returns>A Uniform Resource Identifier (URI) that describes the algorithm to use for XML encryption.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptionMethod.KeySize">
      <summary>Gets or sets the algorithm key size used for XML encryption.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">The <see cref="P:System.Security.Cryptography.Xml.EncryptionMethod.KeySize" /> property was set to a value that was less than 0.</exception>
      <returns>The algorithm key size, in bits, used for XML encryption.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.EncryptionProperty">
      <summary>Represents the <see langword="&lt;EncryptionProperty&gt;" /> element used in XML encryption. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionProperty.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionProperty.#ctor(System.Xml.XmlElement)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> class using an <see cref="T:System.Xml.XmlElement" /> object.</summary>
      <param name="elementProperty">An <see cref="T:System.Xml.XmlElement" /> object to use for initialization.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="elementProperty" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Xml.XmlElement.LocalName" /> property of the <paramref name="elementProperty" /> parameter is not "EncryptionProperty".  
  
 -or-  
  
 The <see cref="P:System.Xml.XmlElement.NamespaceURI" /> property of the <paramref name="elementProperty" /> parameter is not "http://www.w3.org/2001/04/xmlenc#".</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionProperty.GetXml">
      <summary>Returns an <see cref="T:System.Xml.XmlElement" /> object that encapsulates an instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> class.</summary>
      <returns>An <see cref="T:System.Xml.XmlElement" /> object that encapsulates an instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> class.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionProperty.LoadXml(System.Xml.XmlElement)">
      <summary>Parses the input <see cref="T:System.Xml.XmlElement" /> and configures the internal state of the <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object to match.</summary>
      <param name="value">An <see cref="T:System.Xml.XmlElement" /> object to parse.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Xml.XmlElement.LocalName" /> property of the <paramref name="value" /> parameter is not "EncryptionProperty".  
  
 -or-  
  
 The <see cref="P:System.Xml.XmlElement.NamespaceURI" /> property of the <paramref name="value" /> parameter is not "http://www.w3.org/2001/04/xmlenc#".</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptionProperty.Id">
      <summary>Gets the ID of the current <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object.</summary>
      <returns>The ID of the current <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptionProperty.PropertyElement">
      <summary>Gets or sets an <see cref="T:System.Xml.XmlElement" /> object that represents an <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object.</summary>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Security.Cryptography.Xml.EncryptionProperty.PropertyElement" /> property was set to <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Xml.XmlElement.LocalName" /> property of the value set to the <see cref="P:System.Security.Cryptography.Xml.EncryptionProperty.PropertyElement" /> property is not "EncryptionProperty".  
  
 -or-  
  
 The <see cref="P:System.Xml.XmlElement.NamespaceURI" /> property of the value set to the <see cref="P:System.Security.Cryptography.Xml.EncryptionProperty.PropertyElement" /> property is not "http://www.w3.org/2001/04/xmlenc#".</exception>
      <returns>An <see cref="T:System.Xml.XmlElement" /> object that represents an <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptionProperty.Target">
      <summary>Gets the target of the <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object.</summary>
      <returns>The target of the <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection">
      <summary>Represents a collection of <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> classes used in XML encryption. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.Add(System.Security.Cryptography.Xml.EncryptionProperty)">
      <summary>Adds an <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object to the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</summary>
      <param name="value">An <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object to add to the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</param>
      <returns>The position at which the new element is inserted.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.Clear">
      <summary>Removes all items from the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.Contains(System.Security.Cryptography.Xml.EncryptionProperty)">
      <summary>Determines whether the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object contains a specific <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object.</summary>
      <param name="value">The <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object to locate in the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object is found in the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object to an array, starting at a particular array index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> object that is the destination of the elements copied from the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.CopyTo(System.Security.Cryptography.Xml.EncryptionProperty[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object to an array of <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> objects, starting at a particular array index.</summary>
      <param name="array">The one-dimensional array of  <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> objects that is the destination of the elements copied from the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.GetEnumerator">
      <summary>Returns an enumerator that iterates through an <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through an <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.IndexOf(System.Security.Cryptography.Xml.EncryptionProperty)">
      <summary>Determines the index of a specific item in the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</summary>
      <param name="value">The <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object to locate in the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</param>
      <returns>The index of <paramref name="value" /> if found in the collection; otherwise, -1.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.Insert(System.Int32,System.Security.Cryptography.Xml.EncryptionProperty)">
      <summary>Inserts an <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object into the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object at the specified position.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> should be inserted.</param>
      <param name="value">An <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object to insert into the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.Item(System.Int32)">
      <summary>Returns the <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object at the specified index.</summary>
      <param name="index">The index of the <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object to return.</param>
      <returns>The <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object at the specified index.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.Remove(System.Security.Cryptography.Xml.EncryptionProperty)">
      <summary>Removes the first occurrence of a specific <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object from the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</summary>
      <param name="value">The <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object to remove from the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.RemoveAt(System.Int32)">
      <summary>Removes the <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object at the specified index.</summary>
      <param name="index">The zero-based index of the <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object to remove.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.System#Collections#IList#Add(System.Object)">
      <summary>Adds an item to the <see cref="T:System.Collections.IList" />.</summary>
      <param name="value">The <see cref="T:System.Object" /> to add to the <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> uses an incorrect object type.</exception>
      <returns>The position into which the new element was inserted.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.System#Collections#IList#Contains(System.Object)">
      <summary>Determines whether the <see cref="T:System.Collections.IList" /> contains a specific value.</summary>
      <param name="value">The <see cref="T:System.Object" /> to locate in the <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> uses an incorrect object type.</exception>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Object" /> is found in the <see cref="T:System.Collections.IList" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.System#Collections#IList#IndexOf(System.Object)">
      <summary>Determines the index of a specific item in the <see cref="T:System.Collections.IList" />.</summary>
      <param name="value">The <see cref="T:System.Object" /> to locate in the <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> uses an incorrect object type.</exception>
      <returns>The index of <paramref name="value" /> if found in the list; otherwise, -1.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Inserts an item to the <see cref="T:System.Collections.IList" /> at the specified index.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> should be inserted.</param>
      <param name="value">The <see cref="T:System.Object" /> to insert into the <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> uses an incorrect object type.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.EncryptionPropertyCollection.System#Collections#IList#Remove(System.Object)">
      <summary>Removes the first occurrence of a specific object from the <see cref="T:System.Collections.IList" />.</summary>
      <param name="value">The <see cref="T:System.Object" /> to remove from the <see cref="T:System.Collections.IList" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> uses an incorrect object type.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptionPropertyCollection.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptionPropertyCollection.IsFixedSize">
      <summary>Gets a value that indicates whether the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object has a fixed size.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object has a fixed size; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptionPropertyCollection.IsReadOnly">
      <summary>Gets a value that indicates whether the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object is read-only.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptionPropertyCollection.IsSynchronized">
      <summary>Gets a value that indicates whether access to the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object is synchronized (thread safe); otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptionPropertyCollection.ItemOf(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object at the specified index.</summary>
      <param name="index">The index of the <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object to return.</param>
      <returns>The <see cref="T:System.Security.Cryptography.Xml.EncryptionProperty" /> object at the specified index.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptionPropertyCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Security.Cryptography.Xml.EncryptionPropertyCollection" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.EncryptionPropertyCollection.System#Collections#IList#Item(System.Int32)">
      <summary>Gets the element at the specified index.</summary>
      <param name="index">The <see cref="T:System.Object" /> to remove from the <see cref="T:System.Collections.IList" />.</param>
      <returns>The element at the specified index.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.IRelDecryptor">
      <summary>Defines methods that decrypt an XrML <see langword="&lt;encryptedGrant&gt;" /> element.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.IRelDecryptor.Decrypt(System.Security.Cryptography.Xml.EncryptionMethod,System.Security.Cryptography.Xml.KeyInfo,System.IO.Stream)">
      <summary>Decrypts an XrML <see langword="&lt;encryptedGrant&gt;" /> element that is contained within a <see cref="T:System.IO.Stream" /> object.</summary>
      <param name="encryptionMethod">An <see cref="T:System.Security.Cryptography.Xml.EncryptionMethod" /> object that encapsulates the algorithm used for XML encryption.</param>
      <param name="keyInfo">A <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> object that contains an asymmetric key to use for decryption.</param>
      <param name="toDecrypt">A stream object that contains an <see langword="&lt;encryptedGrant&gt;" /> element to decrypt.</param>
      <returns>A <see cref="T:System.IO.Stream" /> object that contains a decrypted <see langword="&lt;encryptedGrant&gt;" /> element.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.KeyInfo">
      <summary>Represents an XML digital signature or XML encryption <see langword="&lt;KeyInfo&gt;" /> element.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfo.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfo.AddClause(System.Security.Cryptography.Xml.KeyInfoClause)">
      <summary>Adds a <see cref="T:System.Security.Cryptography.Xml.KeyInfoClause" /> that represents a particular type of <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> information to the <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> object.</summary>
      <param name="clause">The <see cref="T:System.Security.Cryptography.Xml.KeyInfoClause" /> to add to the <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfo.GetEnumerator">
      <summary>Returns an enumerator of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoClause" /> objects in the <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> object.</summary>
      <returns>An enumerator of the subelements of <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfo.GetEnumerator(System.Type)">
      <summary>Returns an enumerator of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoClause" /> objects of the specified type in the <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> object.</summary>
      <param name="requestedObjectType">The type of object to enumerate.</param>
      <returns>An enumerator of the subelements of <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfo.GetXml">
      <summary>Returns the XML representation of the <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> object.</summary>
      <returns>The XML representation of the <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfo.LoadXml(System.Xml.XmlElement)">
      <summary>Loads a <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> state from an XML element.</summary>
      <param name="value">The XML element from which to load the <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> state.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.KeyInfo.Count">
      <summary>Gets the number of <see cref="T:System.Security.Cryptography.Xml.KeyInfoClause" /> objects contained in the <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> object.</summary>
      <returns>The number of <see cref="T:System.Security.Cryptography.Xml.KeyInfoClause" /> objects contained in the <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.KeyInfo.Id">
      <summary>Gets or sets the key information identity.</summary>
      <returns>The key information identity.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.KeyInfoClause">
      <summary>Represents the abstract base class from which all implementations of <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> subelements inherit.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoClause.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Security.Cryptography.Xml.KeyInfoClause" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoClause.GetXml">
      <summary>When overridden in a derived class, returns an XML representation of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoClause" />.</summary>
      <returns>An XML representation of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoClause" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoClause.LoadXml(System.Xml.XmlElement)">
      <summary>When overridden in a derived class, parses the input <see cref="T:System.Xml.XmlElement" /> and configures the internal state of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoClause" /> to match.</summary>
      <param name="element">The <see cref="T:System.Xml.XmlElement" /> that specifies the state of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoClause" />.</param>
    </member>
    <member name="T:System.Security.Cryptography.Xml.KeyInfoEncryptedKey">
      <summary>Wraps the <see cref="T:System.Security.Cryptography.Xml.EncryptedKey" /> class, it to be placed as a subelement of the <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoEncryptedKey.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoEncryptedKey" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoEncryptedKey.#ctor(System.Security.Cryptography.Xml.EncryptedKey)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoEncryptedKey" /> class using an <see cref="T:System.Security.Cryptography.Xml.EncryptedKey" /> object.</summary>
      <param name="encryptedKey">An <see cref="T:System.Security.Cryptography.Xml.EncryptedKey" /> object that encapsulates an encrypted key.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoEncryptedKey.GetXml">
      <summary>Returns an XML representation of a <see cref="T:System.Security.Cryptography.Xml.KeyInfoEncryptedKey" /> object.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The encrypted key is <see langword="null" />.</exception>
      <returns>An XML representation of a <see cref="T:System.Security.Cryptography.Xml.KeyInfoEncryptedKey" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoEncryptedKey.LoadXml(System.Xml.XmlElement)">
      <summary>Parses the input <see cref="T:System.Xml.XmlElement" /> object and configures the internal state of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoEncryptedKey" /> object to match.</summary>
      <param name="value">The <see cref="T:System.Xml.XmlElement" /> object that specifies the state of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoEncryptedKey" /> object.</param>
    </member>
    <member name="P:System.Security.Cryptography.Xml.KeyInfoEncryptedKey.EncryptedKey">
      <summary>Gets or sets an <see cref="T:System.Security.Cryptography.Xml.EncryptedKey" /> object that encapsulates an encrypted key.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.Xml.KeyInfoEncryptedKey.EncryptedKey" /> property is <see langword="null" />.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.Xml.EncryptedKey" /> object that encapsulates an encrypted key.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.KeyInfoName">
      <summary>Represents a <see langword="&lt;KeyName&gt;" /> subelement of an XMLDSIG or XML Encryption <see langword="&lt;KeyInfo&gt;" /> element.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoName.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoName" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoName.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoName" /> class by specifying the string identifier that is the value of the <see langword="&lt;KeyName&gt;" /> element.</summary>
      <param name="keyName">The string identifier that is the value of the <see langword="&lt;KeyName&gt;" /> element.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoName.GetXml">
      <summary>Returns an XML representation of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoName" /> object.</summary>
      <returns>An XML representation of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoName" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoName.LoadXml(System.Xml.XmlElement)">
      <summary>Parses the input <see cref="T:System.Xml.XmlElement" /> object and configures the internal state of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoName" /> object to match.</summary>
      <param name="value">The <see cref="T:System.Xml.XmlElement" /> object that specifies the state of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoName" /> object.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.KeyInfoName.Value">
      <summary>Gets or sets the string identifier contained within a <see langword="&lt;KeyName&gt;" /> element.</summary>
      <returns>The string identifier that is the value of the <see langword="&lt;KeyName&gt;" /> element.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.KeyInfoNode">
      <summary>Handles <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> subelements that do not have specific implementations or handlers registered on the machine.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoNode.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoNode" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoNode.#ctor(System.Xml.XmlElement)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoNode" /> class with content taken from the specified <see cref="T:System.Xml.XmlElement" />.</summary>
      <param name="node">An XML element from which to take the content used to create the new instance of <see cref="T:System.Security.Cryptography.Xml.KeyInfoNode" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoNode.GetXml">
      <summary>Returns an XML representation of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoNode" />.</summary>
      <returns>An XML representation of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoNode" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoNode.LoadXml(System.Xml.XmlElement)">
      <summary>Parses the input <see cref="T:System.Xml.XmlElement" /> and configures the internal state of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoNode" /> to match.</summary>
      <param name="value">The <see cref="T:System.Xml.XmlElement" /> that specifies the state of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoNode" />.</param>
    </member>
    <member name="P:System.Security.Cryptography.Xml.KeyInfoNode.Value">
      <summary>Gets or sets the XML content of the current <see cref="T:System.Security.Cryptography.Xml.KeyInfoNode" />.</summary>
      <returns>The XML content of the current <see cref="T:System.Security.Cryptography.Xml.KeyInfoNode" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod">
      <summary>References <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> objects stored at a different location when using XMLDSIG or XML encryption.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod" /> class with the specified Uniform Resource Identifier (URI) pointing to the referenced <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> object.</summary>
      <param name="strUri">The Uniform Resource Identifier (URI) of the information to be referenced by the new instance of <see cref="T:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod" /> class with the specified Uniform Resource Identifier (URI) pointing to the referenced <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> object and the URI that describes the type of data to retrieve.</summary>
      <param name="strUri">The Uniform Resource Identifier (URI) of the information to be referenced by the new instance of <see cref="T:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod" />.</param>
      <param name="typeName">The URI that describes the type of data to retrieve.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod.GetXml">
      <summary>Returns the XML representation of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod" /> object.</summary>
      <returns>The XML representation of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod.LoadXml(System.Xml.XmlElement)">
      <summary>Parses the input <see cref="T:System.Xml.XmlElement" /> object and configures the internal state of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod" /> object to match.</summary>
      <param name="value">The XML element that specifies the state of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod" /> object.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod.Type">
      <summary>Gets or sets a Uniform Resource Identifier (URI) that describes the type of data to be retrieved.</summary>
      <returns>A Uniform Resource Identifier (URI) that describes the type of data to be retrieved.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod.Uri">
      <summary>Gets or sets the Uniform Resource Identifier (URI) of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod" /> object.</summary>
      <returns>The Uniform Resource Identifier (URI) of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoRetrievalMethod" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.KeyInfoX509Data">
      <summary>Represents an <see langword="&lt;X509Data&gt;" /> subelement of an XMLDSIG or XML Encryption <see langword="&lt;KeyInfo&gt;" /> element.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoX509Data.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoX509Data.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> class from the specified ASN.1 DER encoding of an X.509v3 certificate.</summary>
      <param name="rgbCert">The ASN.1 DER encoding of an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object to initialize the new instance of <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> from.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoX509Data.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> class from the specified X.509v3 certificate.</summary>
      <param name="cert">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object to initialize the new instance of <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> from.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="cert" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoX509Data.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509IncludeOption)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> class from the specified X.509v3 certificate.</summary>
      <param name="cert">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object to initialize the new instance of <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> from.</param>
      <param name="includeOption">One of the <see cref="T:System.Security.Cryptography.X509Certificates.X509IncludeOption" /> values that specifies how much of the certificate chain to include.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="cert" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate has only a partial certificate chain.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoX509Data.AddCertificate(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Adds the specified X.509v3 certificate to the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" />.</summary>
      <param name="certificate">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object to add to the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="certificate" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoX509Data.AddIssuerSerial(System.String,System.String)">
      <summary>Adds the specified issuer name and serial number pair to the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</summary>
      <param name="issuerName">The issuer name portion of the pair to add to the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</param>
      <param name="serialNumber">The serial number portion of the pair to add to the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoX509Data.AddSubjectKeyId(System.Byte[])">
      <summary>Adds the specified subject key identifier (SKI) byte array to the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</summary>
      <param name="subjectKeyId">A byte array that represents the subject key identifier (SKI) to add to the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoX509Data.AddSubjectKeyId(System.String)">
      <summary>Adds the specified subject key identifier (SKI) string to the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</summary>
      <param name="subjectKeyId">A string that represents the subject key identifier (SKI) to add to the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoX509Data.AddSubjectName(System.String)">
      <summary>Adds the subject name of the entity that was issued an X.509v3 certificate to the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</summary>
      <param name="subjectName">The name of the entity that was issued an X.509 certificate to add to the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoX509Data.GetXml">
      <summary>Returns an XML representation of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</summary>
      <returns>An XML representation of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyInfoX509Data.LoadXml(System.Xml.XmlElement)">
      <summary>Parses the input <see cref="T:System.Xml.XmlElement" /> object and configures the internal state of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object to match.</summary>
      <param name="element">The <see cref="T:System.Xml.XmlElement" /> object that specifies the state of the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="element" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="element" /> parameter does not contain an &lt;<see langword="X509IssuerName" />&gt; node.  
  
 -or-  
  
 The <paramref name="element" /> parameter does not contain an &lt;<see langword="X509SerialNumber" />&gt; node.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.KeyInfoX509Data.Certificates">
      <summary>Gets a list of the X.509v3 certificates contained in the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</summary>
      <returns>A list of the X.509 certificates contained in the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.KeyInfoX509Data.CRL">
      <summary>Gets or sets the Certificate Revocation List (CRL) contained within the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</summary>
      <returns>The Certificate Revocation List (CRL) contained within the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.KeyInfoX509Data.IssuerSerials">
      <summary>Gets a list of <see cref="T:System.Security.Cryptography.Xml.X509IssuerSerial" /> structures that represent an issuer name and serial number pair.</summary>
      <returns>A list of <see cref="T:System.Security.Cryptography.Xml.X509IssuerSerial" /> structures that represent an issuer name and serial number pair.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.KeyInfoX509Data.SubjectKeyIds">
      <summary>Gets a list of the subject key identifiers (SKIs) contained in the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</summary>
      <returns>A list of the subject key identifiers (SKIs) contained in the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.KeyInfoX509Data.SubjectNames">
      <summary>Gets a list of the subject names of the entities contained in the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</summary>
      <returns>A list of the subject names of the entities contained in the <see cref="T:System.Security.Cryptography.Xml.KeyInfoX509Data" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.KeyReference">
      <summary>Represents the <see langword="&lt;KeyReference&gt;" /> element used in XML encryption. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyReference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> class for XML encryption.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyReference.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> class for XML encryption using the supplied Uniform Resource Identifier (URI).</summary>
      <param name="uri">A Uniform Resource Identifier (URI) that points to the encrypted key.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.KeyReference.#ctor(System.String,System.Security.Cryptography.Xml.TransformChain)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> class for XML encryption using the specified Uniform Resource Identifier (URI) and a <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> object.</summary>
      <param name="uri">A Uniform Resource Identifier (URI) that points to the encrypted key.</param>
      <param name="transformChain">A <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> object that describes transforms to do on the encrypted key.</param>
    </member>
    <member name="T:System.Security.Cryptography.Xml.Reference">
      <summary>Represents the <see langword="&lt;reference&gt;" /> element of an XML signature.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Reference.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.Reference" /> class with default properties.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Reference.#ctor(System.IO.Stream)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.Reference" /> class with a hash value of the specified <see cref="T:System.IO.Stream" />.</summary>
      <param name="stream">The <see cref="T:System.IO.Stream" /> with which to initialize the new instance of <see cref="T:System.Security.Cryptography.Xml.Reference" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Reference.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.Reference" /> class with the specified <see cref="T:System.Uri" />.</summary>
      <param name="uri">The <see cref="T:System.Uri" /> with which to initialize the new instance of <see cref="T:System.Security.Cryptography.Xml.Reference" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Reference.AddTransform(System.Security.Cryptography.Xml.Transform)">
      <summary>Adds a <see cref="T:System.Security.Cryptography.Xml.Transform" /> object to the list of transforms to be performed on the data before passing it to the digest algorithm.</summary>
      <param name="transform">The transform to be added to the list of transforms.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="transform" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Reference.GetXml">
      <summary>Returns the XML representation of the <see cref="T:System.Security.Cryptography.Xml.Reference" />.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.Xml.Reference.DigestMethod" /> property is <see langword="null" />.  
  
 -or-  
  
 The <see cref="P:System.Security.Cryptography.Xml.Reference.DigestValue" /> property is <see langword="null" />.</exception>
      <returns>The XML representation of the <see cref="T:System.Security.Cryptography.Xml.Reference" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Reference.LoadXml(System.Xml.XmlElement)">
      <summary>Loads a <see cref="T:System.Security.Cryptography.Xml.Reference" /> state from an XML element.</summary>
      <param name="value">The XML element from which to load the <see cref="T:System.Security.Cryptography.Xml.Reference" /> state.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="value" /> parameter does not contain any transforms.  
  
 -or-  
  
 The <paramref name="value" /> parameter contains an unknown transform.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Reference.DigestMethod">
      <summary>Gets or sets the digest method Uniform Resource Identifier (URI) of the current <see cref="T:System.Security.Cryptography.Xml.Reference" />.</summary>
      <returns>The digest method URI of the current <see cref="T:System.Security.Cryptography.Xml.Reference" />. The default value is <c>http://www.w3.org/2001/04/xmlenc#sha256</c>.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Reference.DigestValue">
      <summary>Gets or sets the digest value of the current <see cref="T:System.Security.Cryptography.Xml.Reference" />.</summary>
      <returns>The digest value of the current <see cref="T:System.Security.Cryptography.Xml.Reference" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Reference.Id">
      <summary>Gets or sets the ID of the current <see cref="T:System.Security.Cryptography.Xml.Reference" />.</summary>
      <returns>The ID of the current <see cref="T:System.Security.Cryptography.Xml.Reference" />. The default is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Reference.TransformChain">
      <summary>Gets the transform chain of the current <see cref="T:System.Security.Cryptography.Xml.Reference" />.</summary>
      <returns>The transform chain of the current <see cref="T:System.Security.Cryptography.Xml.Reference" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Reference.Type">
      <summary>Gets or sets the type of the object being signed.</summary>
      <returns>The type of the object being signed.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Reference.Uri">
      <summary>Gets or sets the <see cref="T:System.Uri" /> of the current <see cref="T:System.Security.Cryptography.Xml.Reference" />.</summary>
      <returns>The <see cref="T:System.Uri" /> of the current <see cref="T:System.Security.Cryptography.Xml.Reference" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.ReferenceList">
      <summary>Represents the <see langword="&lt;ReferenceList&gt;" /> element used in XML encryption. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.ReferenceList.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.ReferenceList.Add(System.Object)">
      <summary>Adds a <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object to the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> collection.</summary>
      <param name="value">A <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object to add to the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> collection.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="value" /> parameter is not a <see cref="T:System.Security.Cryptography.Xml.DataReference" /> object.  
  
 -or-  
  
 The <paramref name="value" /> parameter is not a <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
      <returns>The position at which the new element was inserted.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.ReferenceList.Clear">
      <summary>Removes all items from the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> collection.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.ReferenceList.Contains(System.Object)">
      <summary>Determines whether the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> collection contains a specific <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object.</summary>
      <param name="value">The <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object to locate in the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> collection.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object is found in the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.ReferenceList.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> object to an array, starting at a specified array index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> object that is the destination of the elements copied from the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> object. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index in <paramref name="array" /> at which copying begins.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.ReferenceList.GetEnumerator">
      <summary>Returns an enumerator that iterates through a <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through a <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.ReferenceList.IndexOf(System.Object)">
      <summary>Determines the index of a specific item in the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> collection.</summary>
      <param name="value">The <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object to locate in the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> collection.</param>
      <returns>The index of <paramref name="value" /> if found in the collection; otherwise, -1.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.ReferenceList.Insert(System.Int32,System.Object)">
      <summary>Inserts a <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object into the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> collection at the specified position.</summary>
      <param name="index">The zero-based index at which <paramref name="value" /> should be inserted.</param>
      <param name="value">A <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object to insert into the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> collection.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="value" /> parameter is not a <see cref="T:System.Security.Cryptography.Xml.DataReference" /> object.  
  
 -or-  
  
 The <paramref name="value" /> parameter is not a <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.ReferenceList.Item(System.Int32)">
      <summary>Returns the <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object at the specified index.</summary>
      <param name="index">The index of the <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object to return.</param>
      <returns>The <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object at the specified index.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.ReferenceList.Remove(System.Object)">
      <summary>Removes the first occurrence of a specific <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object from the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> collection.</summary>
      <param name="value">The <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object to remove from the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> collection.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.ReferenceList.RemoveAt(System.Int32)">
      <summary>Removes the <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object at the specified index.</summary>
      <param name="index">The zero-based index of the <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object to remove.</param>
    </member>
    <member name="P:System.Security.Cryptography.Xml.ReferenceList.Count">
      <summary>Gets the number of elements contained in the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> object.</summary>
      <returns>The number of elements contained in the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.ReferenceList.IsSynchronized">
      <summary>Gets a value that indicates whether access to the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> object is synchronized (thread safe).</summary>
      <returns>
        <see langword="true" /> if access to the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> object is synchronized (thread safe); otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.ReferenceList.ItemOf(System.Int32)">
      <summary>Gets or sets the <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object at the specified index.</summary>
      <param name="index">The index of the <see cref="T:System.Security.Cryptography.Xml.DataReference" /> or <see cref="T:System.Security.Cryptography.Xml.KeyReference" /> object to return.</param>
      <returns>The <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> object at the specified index.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.ReferenceList.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> object.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:System.Security.Cryptography.Xml.ReferenceList" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.ReferenceList.System#Collections#IList#IsFixedSize">
      <summary>For a description of this member, see <see cref="P:System.Collections.IList.IsFixedSize" />.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.IList" /> has a fixed size; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.ReferenceList.System#Collections#IList#IsReadOnly">
      <summary>For a description of this member, see <see cref="P:System.Collections.IList.IsReadOnly" />.</summary>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Collections.IList" /> is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.ReferenceList.System#Collections#IList#Item(System.Int32)">
      <summary>For a description of this member, see <see cref="P:System.Collections.IList.Item(System.Int32)" />.</summary>
      <param name="index">The zero-based index of the element to get or set.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is not a valid index in the <see cref="T:System.Collections.IList" />.</exception>
      <returns>The element at the specified index.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.RSAKeyValue">
      <summary>Represents the &lt;<see langword="RSAKeyValue" />&gt; element of an XML signature.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.RSAKeyValue.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.RSAKeyValue" /> class with a new randomly generated <see cref="T:System.Security.Cryptography.RSA" /> public key.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.RSAKeyValue.#ctor(System.Security.Cryptography.RSA)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.RSAKeyValue" /> class with the specified <see cref="T:System.Security.Cryptography.RSA" /> public key.</summary>
      <param name="key">The instance of an implementation of <see cref="T:System.Security.Cryptography.RSA" /> that holds the public key.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.RSAKeyValue.GetXml">
      <summary>Returns the XML representation of the <see cref="T:System.Security.Cryptography.RSA" /> key clause.</summary>
      <returns>The XML representation of the <see cref="T:System.Security.Cryptography.RSA" /> key clause.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.RSAKeyValue.LoadXml(System.Xml.XmlElement)">
      <summary>Loads an <see cref="T:System.Security.Cryptography.RSA" /> key clause from an XML element.</summary>
      <param name="value">The XML element from which to load the <see cref="T:System.Security.Cryptography.RSA" /> key clause.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="value" /> parameter is not a valid <see cref="T:System.Security.Cryptography.RSA" /> key clause XML element.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.RSAKeyValue.Key">
      <summary>Gets or sets the instance of <see cref="T:System.Security.Cryptography.RSA" /> that holds the public key.</summary>
      <returns>The instance of <see cref="T:System.Security.Cryptography.RSA" /> that holds the public key.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.Signature">
      <summary>Represents the <see langword="&lt;Signature&gt;" /> element of an XML signature.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Signature.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.Signature" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Signature.AddObject(System.Security.Cryptography.Xml.DataObject)">
      <summary>Adds a <see cref="T:System.Security.Cryptography.Xml.DataObject" /> to the list of objects to be signed.</summary>
      <param name="dataObject">The <see cref="T:System.Security.Cryptography.Xml.DataObject" /> to be added to the list of objects to be signed.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Signature.GetXml">
      <summary>Returns the XML representation of the <see cref="T:System.Security.Cryptography.Xml.Signature" />.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.Xml.Signature.SignedInfo" /> property is <see langword="null" />.  
  
 -or-  
  
 The <see cref="P:System.Security.Cryptography.Xml.Signature.SignatureValue" /> property is <see langword="null" />.</exception>
      <returns>The XML representation of the <see cref="T:System.Security.Cryptography.Xml.Signature" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Signature.LoadXml(System.Xml.XmlElement)">
      <summary>Loads a <see cref="T:System.Security.Cryptography.Xml.Signature" /> state from an XML element.</summary>
      <param name="value">The XML element from which to load the <see cref="T:System.Security.Cryptography.Xml.Signature" /> state.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="value" /> parameter does not contain a valid <see cref="P:System.Security.Cryptography.Xml.Signature.SignatureValue" />.  
  
 -or-  
  
 The <paramref name="value" /> parameter does not contain a valid <see cref="P:System.Security.Cryptography.Xml.Signature.SignedInfo" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Signature.Id">
      <summary>Gets or sets the ID of the current <see cref="T:System.Security.Cryptography.Xml.Signature" />.</summary>
      <returns>The ID of the current <see cref="T:System.Security.Cryptography.Xml.Signature" />. The default is <see langword="null" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Signature.KeyInfo">
      <summary>Gets or sets the <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> of the current <see cref="T:System.Security.Cryptography.Xml.Signature" />.</summary>
      <returns>The <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> of the current <see cref="T:System.Security.Cryptography.Xml.Signature" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Signature.ObjectList">
      <summary>Gets or sets a list of objects to be signed.</summary>
      <returns>A list of objects to be signed.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Signature.SignatureValue">
      <summary>Gets or sets the value of the digital signature.</summary>
      <returns>A byte array that contains the value of the digital signature.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Signature.SignedInfo">
      <summary>Gets or sets the <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> of the current <see cref="T:System.Security.Cryptography.Xml.Signature" />.</summary>
      <returns>The <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> of the current <see cref="T:System.Security.Cryptography.Xml.Signature" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.SignedInfo">
      <summary>Contains information about the canonicalization algorithm and signature algorithm used for the XML signature.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedInfo.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedInfo.AddReference(System.Security.Cryptography.Xml.Reference)">
      <summary>Adds a <see cref="T:System.Security.Cryptography.Xml.Reference" /> object to the list of references to digest and sign.</summary>
      <param name="reference">The reference to add to the list of references.</param>
      <exception cref="T:System.ArgumentNullException">The reference parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedInfo.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of this instance into an <see cref="T:System.Array" /> object, starting at a specified index in the array.</summary>
      <param name="array">An <see cref="T:System.Array" /> object that holds the collection's elements.</param>
      <param name="index">The beginning index in the array where the elements are copied.</param>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedInfo.GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection of references.</summary>
      <exception cref="T:System.NotSupportedException">This method is not supported.</exception>
      <returns>An enumerator that iterates through the collection of references.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedInfo.GetXml">
      <summary>Returns the XML representation of the <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.Xml.SignedInfo.SignatureMethod" /> property is <see langword="null" />.  
  
 -or-  
  
 The <see cref="P:System.Security.Cryptography.Xml.SignedInfo.References" /> property is empty.</exception>
      <returns>The XML representation of the <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> instance.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedInfo.LoadXml(System.Xml.XmlElement)">
      <summary>Loads a <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> state from an XML element.</summary>
      <param name="value">The XML element from which to load the <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> state.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="value" /> parameter is not a valid <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> element.  
  
 -or-  
  
 The <paramref name="value" /> parameter does not contain a valid <see cref="P:System.Security.Cryptography.Xml.SignedInfo.CanonicalizationMethod" /> property.  
  
 -or-  
  
 The <paramref name="value" /> parameter does not contain a valid <see cref="P:System.Security.Cryptography.Xml.SignedInfo.SignatureMethod" /> property.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedInfo.CanonicalizationMethod">
      <summary>Gets or sets the canonicalization algorithm that is used before signing for the current <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object.</summary>
      <returns>The canonicalization algorithm used before signing for the current <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedInfo.CanonicalizationMethodObject">
      <summary>Gets a <see cref="T:System.Security.Cryptography.Xml.Transform" /> object used for canonicalization.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <see cref="T:System.Security.Cryptography.Xml.Transform" /> is <see langword="null" />.</exception>
      <returns>A <see cref="T:System.Security.Cryptography.Xml.Transform" /> object used for canonicalization.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedInfo.Count">
      <summary>Gets the number of references in the current <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object.</summary>
      <exception cref="T:System.NotSupportedException">This property is not supported.</exception>
      <returns>The number of references in the current <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedInfo.Id">
      <summary>Gets or sets the ID of the current <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object.</summary>
      <returns>The ID of the current <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedInfo.IsReadOnly">
      <summary>Gets a value that indicates whether the collection is read-only.</summary>
      <exception cref="T:System.NotSupportedException">This property is not supported.</exception>
      <returns>
        <see langword="true" /> if the collection is read-only; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedInfo.IsSynchronized">
      <summary>Gets a value that indicates whether the collection is synchronized.</summary>
      <exception cref="T:System.NotSupportedException">This property is not supported.</exception>
      <returns>
        <see langword="true" /> if the collection is synchronized; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedInfo.References">
      <summary>Gets a list of the <see cref="T:System.Security.Cryptography.Xml.Reference" /> objects of the current <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object.</summary>
      <returns>A list of the <see cref="T:System.Security.Cryptography.Xml.Reference" /> elements of the current <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedInfo.SignatureLength">
      <summary>Gets or sets the length of the signature for the current <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object.</summary>
      <returns>The length of the signature for the current <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedInfo.SignatureMethod">
      <summary>Gets or sets the name of the algorithm used for signature generation and validation for the current <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object.</summary>
      <returns>The name of the algorithm used for signature generation and validation for the current <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedInfo.SyncRoot">
      <summary>Gets an object to use for synchronization.</summary>
      <exception cref="T:System.NotSupportedException">This property is not supported.</exception>
      <returns>An object to use for synchronization.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.SignedXml">
      <summary>Provides a wrapper on a core XML signature object to facilitate creating XML signatures.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.m_signature">
      <summary>Represents the <see cref="T:System.Security.Cryptography.Xml.Signature" /> object of the current <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.m_strSigningKeyName">
      <summary>Represents the name of the installed key to be used for signing the <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDecryptionTransformUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for the XML mode decryption transformation. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigBase64TransformUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for the base 64 transformation. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigC14NTransformUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for the Canonical XML transformation. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigC14NWithCommentsTransformUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for the Canonical XML transformation, with comments. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigCanonicalizationUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for the standard canonicalization algorithm for XML digital signatures. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigCanonicalizationWithCommentsUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for the standard canonicalization algorithm for XML digital signatures and includes comments. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigDSAUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for the standard <see cref="T:System.Security.Cryptography.DSA" /> algorithm for XML digital signatures. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigEnvelopedSignatureTransformUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for enveloped signature transformation. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigExcC14NTransformUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for exclusive XML canonicalization. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigExcC14NWithCommentsTransformUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for exclusive XML canonicalization, with comments. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigHMACSHA1Url">
      <summary>Represents the Uniform Resource Identifier (URI) for the standard <see cref="T:System.Security.Cryptography.HMACSHA1" /> algorithm for XML digital signatures. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigMinimalCanonicalizationUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for the standard minimal canonicalization algorithm for XML digital signatures. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigNamespaceUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for the standard namespace for XML digital signatures. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigRSASHA1Url">
      <summary>Represents the Uniform Resource Identifier (URI) for the standard <see cref="T:System.Security.Cryptography.RSA" /> signature method for XML digital signatures. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigRSASHA256Url">
      <summary>Represents the Uniform Resource Identifier (URI) for the  <see cref="T:System.Security.Cryptography.RSA" /> SHA-256 signature method variation for XML digital signatures. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigRSASHA384Url">
      <summary>Represents the Uniform Resource Identifier (URI) for the  <see cref="T:System.Security.Cryptography.RSA" /> SHA-384 signature method variation for XML digital signatures. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigRSASHA512Url">
      <summary>Represents the Uniform Resource Identifier (URI) for the  <see cref="T:System.Security.Cryptography.RSA" /> SHA-512 signature method variation for XML digital signatures. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigSHA1Url">
      <summary>Represents the Uniform Resource Identifier (URI) for the standard <see cref="T:System.Security.Cryptography.SHA1" /> digest method for XML digital signatures. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigSHA256Url">
      <summary>Represents the Uniform Resource Identifier (URI) for the standard <see cref="T:System.Security.Cryptography.SHA256" /> digest method for XML digital signatures. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigSHA384Url">
      <summary>Represents the Uniform Resource Identifier (URI) for the standard <see cref="T:System.Security.Cryptography.SHA384" /> digest method for XML digital signatures. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigSHA512Url">
      <summary>Represents the Uniform Resource Identifier (URI) for the standard <see cref="T:System.Security.Cryptography.SHA512" /> digest method for XML digital signatures. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigXPathTransformUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for the XML Path Language (XPath). This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlDsigXsltTransformUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for XSLT transformations. This field is constant.</summary>
    </member>
    <member name="F:System.Security.Cryptography.Xml.SignedXml.XmlLicenseTransformUrl">
      <summary>Represents the Uniform Resource Identifier (URI) for the license transform algorithm used to normalize XrML licenses for signatures.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.#ctor(System.Xml.XmlDocument)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> class from the specified XML document.</summary>
      <param name="document">The <see cref="T:System.Xml.XmlDocument" /> object to use to initialize the new instance of <see cref="T:System.Security.Cryptography.Xml.SignedXml" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="document" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The <paramref name="document" /> parameter contains a null <see cref="P:System.Xml.XmlDocument.DocumentElement" /> property.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.#ctor(System.Xml.XmlElement)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> class from the specified <see cref="T:System.Xml.XmlElement" /> object.</summary>
      <param name="elem">The <see cref="T:System.Xml.XmlElement" /> object to use to initialize the new instance of <see cref="T:System.Security.Cryptography.Xml.SignedXml" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="elem" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.AddObject(System.Security.Cryptography.Xml.DataObject)">
      <summary>Adds a <see cref="T:System.Security.Cryptography.Xml.DataObject" /> object to the list of objects to be signed.</summary>
      <param name="dataObject">The <see cref="T:System.Security.Cryptography.Xml.DataObject" /> object to add to the list of objects to be signed.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.AddReference(System.Security.Cryptography.Xml.Reference)">
      <summary>Adds a <see cref="T:System.Security.Cryptography.Xml.Reference" /> object to the <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object that describes a digest method, digest value, and transform to use for creating an XML digital signature.</summary>
      <param name="reference">The  <see cref="T:System.Security.Cryptography.Xml.Reference" /> object that describes a digest method, digest value, and transform to use for creating an XML digital signature.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.CheckSignature">
      <summary>Determines whether the <see cref="P:System.Security.Cryptography.Xml.SignedXml.Signature" /> property verifies using the public key in the signature.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.AsymmetricAlgorithm.SignatureAlgorithm" /> property of the public key in the signature does not match the <see cref="P:System.Security.Cryptography.Xml.SignedXml.SignatureMethod" /> property.  
  
 -or-  
  
 The signature description could not be created.  
  
 -or  
  
 The hash algorithm could not be created.</exception>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Security.Cryptography.Xml.SignedXml.Signature" /> property verifies; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.CheckSignature(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Determines whether the <see cref="P:System.Security.Cryptography.Xml.SignedXml.Signature" /> property verifies for the specified key.</summary>
      <param name="key">The implementation of the <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> property that holds the key to be used to verify the <see cref="P:System.Security.Cryptography.Xml.SignedXml.Signature" /> property.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="key" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.AsymmetricAlgorithm.SignatureAlgorithm" /> property of the <paramref name="key" /> parameter does not match the <see cref="P:System.Security.Cryptography.Xml.SignedXml.SignatureMethod" /> property.  
  
 -or-  
  
 The signature description could not be created.  
  
 -or  
  
 The hash algorithm could not be created.</exception>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Security.Cryptography.Xml.SignedXml.Signature" /> property verifies for the specified key; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.CheckSignature(System.Security.Cryptography.KeyedHashAlgorithm)">
      <summary>Determines whether the <see cref="P:System.Security.Cryptography.Xml.SignedXml.Signature" /> property verifies for the specified message authentication code (MAC) algorithm.</summary>
      <param name="macAlg">The implementation of <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm" /> that holds the MAC to be used to verify the <see cref="P:System.Security.Cryptography.Xml.SignedXml.Signature" /> property.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="macAlg" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.HashAlgorithm.HashSize" /> property of the specified <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm" /> object is not valid.  
  
 -or-  
  
 The <see cref="P:System.Security.Cryptography.Xml.SignedXml.Signature" /> property is <see langword="null" />.  
  
 -or-  
  
 The cryptographic transform used to check the signature could not be created.</exception>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Security.Cryptography.Xml.SignedXml.Signature" /> property verifies for the specified MAC; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.CheckSignature(System.Security.Cryptography.X509Certificates.X509Certificate2,System.Boolean)">
      <summary>Determines whether the <see cref="P:System.Security.Cryptography.Xml.SignedXml.Signature" /> property verifies for the specified <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object and, optionally, whether the certificate is valid.</summary>
      <param name="certificate">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object to use to verify the <see cref="P:System.Security.Cryptography.Xml.SignedXml.Signature" /> property.</param>
      <param name="verifySignatureOnly">
        <see langword="true" /> to verify the signature only; <see langword="false" /> to verify both the signature and certificate.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="certificate" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A signature description could not be created for the <paramref name="certificate" /> parameter.</exception>
      <returns>
        <see langword="true" /> if the signature is valid; otherwise, <see langword="false" />.  
  
 -or-  
  
 <see langword="true" /> if the signature and certificate are valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.CheckSignatureReturningKey(System.Security.Cryptography.AsymmetricAlgorithm@)">
      <summary>Determines whether the <see cref="P:System.Security.Cryptography.Xml.SignedXml.Signature" /> property verifies using the public key in the signature.</summary>
      <param name="signingKey">When this method returns, contains the implementation of <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> that holds the public key in the signature. This parameter is passed uninitialized.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="signingKey" /> parameter is null.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.AsymmetricAlgorithm.SignatureAlgorithm" /> property of the public key in the signature does not match the <see cref="P:System.Security.Cryptography.Xml.SignedXml.SignatureMethod" /> property.  
  
 -or-  
  
 The signature description could not be created.  
  
 -or  
  
 The hash algorithm could not be created.</exception>
      <returns>
        <see langword="true" /> if the <see cref="P:System.Security.Cryptography.Xml.SignedXml.Signature" /> property verifies using the public key in the signature; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.ComputeSignature">
      <summary>Computes an XML digital signature.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.Xml.SignedXml.SigningKey" /> property is <see langword="null" />.  
  
 -or-  
  
 The <see cref="P:System.Security.Cryptography.Xml.SignedXml.SigningKey" /> property is not a <see cref="T:System.Security.Cryptography.DSA" /> object or <see cref="T:System.Security.Cryptography.RSA" /> object.  
  
 -or-  
  
 The key could not be loaded.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.ComputeSignature(System.Security.Cryptography.KeyedHashAlgorithm)">
      <summary>Computes an XML digital signature using the specified message authentication code (MAC) algorithm.</summary>
      <param name="macAlg">A <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm" /> object that holds the MAC to be used to compute the value of the <see cref="P:System.Security.Cryptography.Xml.SignedXml.Signature" /> property.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="macAlg" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm" /> object specified by the <paramref name="macAlg" /> parameter is not an instance of <see cref="T:System.Security.Cryptography.HMACSHA1" /> or <see cref="T:System.Security.Cryptography.HMACSHA256" /> or <see cref="T:System.Security.Cryptography.HMACSHA384" /> or <see cref="T:System.Security.Cryptography.HMACSHA512" />.
  
 -or-  
  
 The <see cref="P:System.Security.Cryptography.HashAlgorithm.HashSize" /> property of the specified <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm" /> object is not valid.  
  
 -or-  
  
 The cryptographic transform used to check the signature could not be created.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.GetIdElement(System.Xml.XmlDocument,System.String)">
      <summary>Returns the <see cref="T:System.Xml.XmlElement" /> object with the specified ID from the specified <see cref="T:System.Xml.XmlDocument" /> object.</summary>
      <param name="document">The <see cref="T:System.Xml.XmlDocument" /> object to retrieve the <see cref="T:System.Xml.XmlElement" /> object from.</param>
      <param name="idValue">The ID of the <see cref="T:System.Xml.XmlElement" /> object to retrieve from the <see cref="T:System.Xml.XmlDocument" /> object.</param>
      <returns>The <see cref="T:System.Xml.XmlElement" /> object with the specified ID from the specified <see cref="T:System.Xml.XmlDocument" /> object, or <see langword="null" /> if it could not be found.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.GetPublicKey">
      <summary>Returns the public key of a signature.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.Xml.SignedXml.KeyInfo" /> property is <see langword="null" />.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> object that contains the public key of the signature, or <see langword="null" /> if the key cannot be found.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.GetXml">
      <summary>Returns the XML representation of a <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <see cref="P:System.Security.Cryptography.Xml.SignedXml.SignedInfo" /> property is <see langword="null" />.  
  
 -or-  
  
 The <see cref="P:System.Security.Cryptography.Xml.SignedXml.SignatureValue" /> property is <see langword="null" />.</exception>
      <returns>The XML representation of the <see cref="T:System.Security.Cryptography.Xml.Signature" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.SignedXml.LoadXml(System.Xml.XmlElement)">
      <summary>Loads a <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> state from an XML element.</summary>
      <param name="value">The XML element to load the <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> state from.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="value" /> parameter does not contain a valid <see cref="P:System.Security.Cryptography.Xml.SignedXml.SignatureValue" /> property.  
  
 -or-  
  
 The <paramref name="value" /> parameter does not contain a valid <see cref="P:System.Security.Cryptography.Xml.SignedXml.SignedInfo" /> property.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedXml.EncryptedXml">
      <summary>Gets or sets an <see cref="T:System.Security.Cryptography.Xml.EncryptedXml" /> object that defines the XML encryption processing rules.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.Xml.EncryptedXml" /> object that defines the XML encryption processing rules.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedXml.KeyInfo">
      <summary>Gets or sets the <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> object of the current <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</summary>
      <returns>The <see cref="T:System.Security.Cryptography.Xml.KeyInfo" /> object of the current <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedXml.Resolver">
      <summary>Sets the current <see cref="T:System.Xml.XmlResolver" /> object.</summary>
      <returns>The current <see cref="T:System.Xml.XmlResolver" /> object. The defaults is a <see cref="T:System.Xml.XmlSecureResolver" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedXml.SafeCanonicalizationMethods">
      <summary>Gets the names of methods whose canonicalization algorithms are explicitly allowed.</summary>
      <returns>A collection of the names of methods that safely produce canonical XML.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedXml.Signature">
      <summary>Gets the <see cref="T:System.Security.Cryptography.Xml.Signature" /> object of the current <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</summary>
      <returns>The <see cref="T:System.Security.Cryptography.Xml.Signature" /> object of the current <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedXml.SignatureFormatValidator">
      <summary>Gets a delegate that will be called to validate the format (not the cryptographic security) of an XML signature.</summary>
      <returns>
        <see langword="true" /> if the format is acceptable; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedXml.SignatureLength">
      <summary>Gets the length of the signature for the current <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</summary>
      <returns>The length of the signature for the current <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedXml.SignatureMethod">
      <summary>Gets the signature method of the current <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</summary>
      <returns>The signature method of the current <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedXml.SignatureValue">
      <summary>Gets the signature value of the current <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</summary>
      <returns>A byte array that contains the signature value of the current <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedXml.SignedInfo">
      <summary>Gets the <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object of the current <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</summary>
      <returns>The <see cref="T:System.Security.Cryptography.Xml.SignedInfo" /> object of the current <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedXml.SigningKey">
      <summary>Gets or sets the asymmetric algorithm key used for signing a <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</summary>
      <returns>The asymmetric algorithm key used for signing the <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.SignedXml.SigningKeyName">
      <summary>Gets or sets the name of the installed key to be used for signing the <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</summary>
      <returns>The name of the installed key to be used for signing the <see cref="T:System.Security.Cryptography.Xml.SignedXml" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.Transform">
      <summary>Represents the abstract base class from which all <see langword="&lt;Transform&gt;" /> elements that can be used in an XML digital signature derive.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Transform.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.Transform" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Transform.GetDigestedOutput(System.Security.Cryptography.HashAlgorithm)">
      <summary>When overridden in a derived class, returns the digest associated with a <see cref="T:System.Security.Cryptography.Xml.Transform" /> object.</summary>
      <param name="hash">The <see cref="T:System.Security.Cryptography.HashAlgorithm" /> object used to create a digest.</param>
      <returns>The digest associated with a <see cref="T:System.Security.Cryptography.Xml.Transform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Transform.GetInnerXml">
      <summary>When overridden in a derived class, returns an XML representation of the parameters of the <see cref="T:System.Security.Cryptography.Xml.Transform" /> object that are suitable to be included as subelements of an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</summary>
      <returns>A list of the XML nodes that represent the transform-specific content needed to describe the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object in an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Transform.GetOutput">
      <summary>When overridden in a derived class, returns the output of the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object.</summary>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Transform.GetOutput(System.Type)">
      <summary>When overridden in a derived class, returns the output of the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object of the specified type.</summary>
      <param name="type">The type of the output to return. This must be one of the types in the <see cref="P:System.Security.Cryptography.Xml.Transform.OutputTypes" /> property.</param>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object as an object of the specified type.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Transform.GetXml">
      <summary>Returns the XML representation of the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object.</summary>
      <returns>The XML representation of the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Transform.LoadInnerXml(System.Xml.XmlNodeList)">
      <summary>When overridden in a derived class, parses the specified <see cref="T:System.Xml.XmlNodeList" /> object as transform-specific content of a <see langword="&lt;Transform&gt;" /> element and configures the internal state of the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object to match the <see langword="&lt;Transform&gt;" /> element.</summary>
      <param name="nodeList">An <see cref="T:System.Xml.XmlNodeList" /> object that specifies transform-specific content for the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.Transform.LoadInput(System.Object)">
      <summary>When overridden in a derived class, loads the specified input into the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object.</summary>
      <param name="obj">The input to load into the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object.</param>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Transform.Algorithm">
      <summary>Gets or sets the Uniform Resource Identifier (URI) that identifies the algorithm performed by the current transform.</summary>
      <returns>The URI that identifies the algorithm performed by the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Transform.Context">
      <summary>Gets or sets an <see cref="T:System.Xml.XmlElement" /> object that represents the document context under which the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object is running.</summary>
      <returns>An <see cref="T:System.Xml.XmlElement" /> object that represents the document context under which the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object is running.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Transform.InputTypes">
      <summary>When overridden in a derived class, gets an array of types that are valid inputs to the <see cref="M:System.Security.Cryptography.Xml.Transform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object.</summary>
      <returns>An array of valid input types for the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object; you can pass only objects of one of these types to the <see cref="M:System.Security.Cryptography.Xml.Transform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Transform.OutputTypes">
      <summary>When overridden in a derived class, gets an array of types that are possible outputs from the <see cref="M:System.Security.Cryptography.Xml.Transform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object.</summary>
      <returns>An array of valid output types for the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object; only objects of one of these types are returned from the <see cref="M:System.Security.Cryptography.Xml.Transform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.Transform" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Transform.PropagatedNamespaces">
      <summary>Gets or sets a <see cref="T:System.Collections.Hashtable" /> object that contains the namespaces that are propagated into the signature.</summary>
      <exception cref="T:System.ArgumentNullException">The <see cref="P:System.Security.Cryptography.Xml.Transform.PropagatedNamespaces" /> property was set to <see langword="null" />.</exception>
      <returns>A <see cref="T:System.Collections.Hashtable" /> object that contains the namespaces that are propagated into the signature.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.Transform.Resolver">
      <summary>Sets the current <see cref="T:System.Xml.XmlResolver" /> object.</summary>
      <returns>The current <see cref="T:System.Xml.XmlResolver" /> object. This property defaults to an <see cref="T:System.Xml.XmlSecureResolver" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.TransformChain">
      <summary>Defines an ordered list of <see cref="T:System.Security.Cryptography.Xml.Transform" /> objects that is applied to unsigned content prior to digest calculation.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.TransformChain.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.TransformChain.Add(System.Security.Cryptography.Xml.Transform)">
      <summary>Adds a transform to the list of transforms to be applied to the unsigned content prior to digest calculation.</summary>
      <param name="transform">The transform to add to the list of transforms.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.TransformChain.GetEnumerator">
      <summary>Returns an enumerator of the transforms in the <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> object.</summary>
      <returns>An enumerator of the transforms in the <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.TransformChain.Count">
      <summary>Gets the number of transforms in the <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> object.</summary>
      <returns>The number of transforms in the <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.TransformChain.Item(System.Int32)">
      <summary>Gets the transform at the specified index in the <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> object.</summary>
      <param name="index">The index into the <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> object that specifies which transform to return.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="index" /> parameter is greater than the number of transforms.</exception>
      <returns>The transform at the specified index in the <see cref="T:System.Security.Cryptography.Xml.TransformChain" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.XmlDecryptionTransform">
      <summary>Specifies the order of XML Digital Signature and XML Encryption operations when both are performed on the same document.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDecryptionTransform.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDecryptionTransform" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDecryptionTransform.AddExceptUri(System.String)">
      <summary>Adds a Uniform Resource Identifier (URI) to exclude from processing.</summary>
      <param name="uri">A Uniform Resource Identifier (URI) to exclude from processing.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="uri" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDecryptionTransform.GetInnerXml">
      <summary>Returns an XML representation of the parameters of an <see cref="T:System.Security.Cryptography.Xml.XmlDecryptionTransform" /> object that are suitable to be included as subelements of an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</summary>
      <returns>A list of the XML nodes that represent the transform-specific content needed to describe the current <see cref="T:System.Security.Cryptography.Xml.XmlDecryptionTransform" /> object in an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDecryptionTransform.GetOutput">
      <summary>Returns the output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A decryption key could not be found.</exception>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDecryptionTransform.GetOutput(System.Type)">
      <summary>Returns the output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object.</summary>
      <param name="type">The type of the output to return. <see cref="T:System.Xml.XmlNodeList" /> is the only valid type for this parameter.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="type" /> parameter is not an <see cref="T:System.Xml.XmlNodeList" /> object.</exception>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDecryptionTransform.IsTargetElement(System.Xml.XmlElement,System.String)">
      <summary>Determines whether the ID attribute of an <see cref="T:System.Xml.XmlElement" /> object matches a specified value.</summary>
      <param name="inputElement">An <see cref="T:System.Xml.XmlElement" /> object with an ID attribute to compare with <paramref name="idValue" />.</param>
      <param name="idValue">The value to compare with the ID attribute of <paramref name="inputElement" />.</param>
      <returns>
        <see langword="true" /> if the ID attribute of the <paramref name="inputElement" /> parameter matches the <paramref name="idValue" /> parameter; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDecryptionTransform.LoadInnerXml(System.Xml.XmlNodeList)">
      <summary>Parses the specified <see cref="T:System.Xml.XmlNodeList" /> object as transform-specific content of a <see langword="&lt;Transform&gt;" /> element and configures the internal state of the current <see cref="T:System.Security.Cryptography.Xml.XmlDecryptionTransform" /> object to match the <see langword="&lt;Transform&gt;" /> element.</summary>
      <param name="nodeList">An <see cref="T:System.Xml.XmlNodeList" /> object that specifies transform-specific content for the current <see cref="T:System.Security.Cryptography.Xml.XmlDecryptionTransform" /> object.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="nodeList" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The Uniform Resource Identifier (URI) value of an <see cref="T:System.Xml.XmlNode" /> object in <paramref name="nodeList" /> was not found.  
  
 -or-  
  
 The length of the URI value of an <see cref="T:System.Xml.XmlNode" /> object in <paramref name="nodeList" /> is 0.  
  
 -or-  
  
 The first character of the URI value of an <see cref="T:System.Xml.XmlNode" /> object in <paramref name="nodeList" /> is not '#'.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDecryptionTransform.LoadInput(System.Object)">
      <summary>When overridden in a derived class, loads the specified input into the current <see cref="T:System.Security.Cryptography.Xml.XmlDecryptionTransform" /> object.</summary>
      <param name="obj">The input to load into the current <see cref="T:System.Security.Cryptography.Xml.XmlDecryptionTransform" /> object.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="obj" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDecryptionTransform.EncryptedXml">
      <summary>Gets or sets an <see cref="T:System.Security.Cryptography.Xml.EncryptedXml" /> object that contains information about the keys necessary to decrypt an XML document.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.Xml.EncryptedXml" /> object that contains information about the keys necessary to decrypt an XML document.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDecryptionTransform.InputTypes">
      <summary>Gets an array of types that are valid inputs to the <see cref="M:System.Security.Cryptography.Xml.XmlDecryptionTransform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlDecryptionTransform" /> object.</summary>
      <returns>An array of valid input types for the current <see cref="T:System.Security.Cryptography.Xml.XmlDecryptionTransform" /> object; you can pass only objects of one of these types to the <see cref="M:System.Security.Cryptography.Xml.XmlDecryptionTransform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlDecryptionTransform" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDecryptionTransform.OutputTypes">
      <summary>Gets an array of types that are possible outputs from the <see cref="M:System.Security.Cryptography.Xml.XmlDecryptionTransform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlDecryptionTransform" /> object.</summary>
      <returns>An array of valid output types for the current <see cref="T:System.Security.Cryptography.Xml.XmlDecryptionTransform" /> object; only objects of one of these types are returned from the <see cref="M:System.Security.Cryptography.Xml.XmlDecryptionTransform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlDecryptionTransform" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform">
      <summary>Represents the <see langword="Base64" /> decoding transform as defined in Section 6.6.2 of the XMLDSIG specification.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigBase64Transform.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigBase64Transform.GetInnerXml">
      <summary>Returns an XML representation of the parameters of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object that are suitable to be included as subelements of an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</summary>
      <returns>A list of the XML nodes that represent the transform-specific content needed to describe the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object in an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigBase64Transform.GetOutput">
      <summary>Returns the output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object.</summary>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigBase64Transform.GetOutput(System.Type)">
      <summary>Returns the output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object of type <see cref="T:System.IO.Stream" />.</summary>
      <param name="type">The type of the output to return. <see cref="T:System.IO.Stream" /> is the only valid type for this parameter.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="type" /> parameter is not a <see cref="T:System.IO.Stream" /> object.</exception>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object of type <see cref="T:System.IO.Stream" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigBase64Transform.LoadInnerXml(System.Xml.XmlNodeList)">
      <summary>Parses the specified <see cref="T:System.Xml.XmlNodeList" /> object as transform-specific content of a <see langword="&lt;Transform&gt;" /> element; this method is not supported because the <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object has no inner XML elements.</summary>
      <param name="nodeList">An <see cref="T:System.Xml.XmlNodeList" /> object to load into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigBase64Transform.LoadInput(System.Object)">
      <summary>Loads the specified input into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object.</summary>
      <param name="obj">The input to load into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="obj" /> parameter is a <see cref="T:System.IO.Stream" /> and it is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDsigBase64Transform.InputTypes">
      <summary>Gets an array of types that are valid inputs to the <see cref="M:System.Security.Cryptography.Xml.XmlDsigBase64Transform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object.</summary>
      <returns>An array of valid input types for the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object; you can pass only objects of one of these types to the <see cref="M:System.Security.Cryptography.Xml.XmlDsigBase64Transform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDsigBase64Transform.OutputTypes">
      <summary>Gets an array of types that are possible outputs from the <see cref="M:System.Security.Cryptography.Xml.XmlDsigBase64Transform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object.</summary>
      <returns>An array of valid output types for the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object; only objects of one of these types are returned from the <see cref="M:System.Security.Cryptography.Xml.XmlDsigBase64Transform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigBase64Transform" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform">
      <summary>Represents the C14N XML canonicalization transform for a digital signature as defined by the World Wide Web Consortium (W3C), without comments.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigC14NTransform.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigC14NTransform.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> class with comments, if specified.</summary>
      <param name="includeComments">
        <see langword="true" /> to include comments; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigC14NTransform.GetDigestedOutput(System.Security.Cryptography.HashAlgorithm)">
      <summary>Returns the digest associated with an <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object.</summary>
      <param name="hash">The <see cref="T:System.Security.Cryptography.HashAlgorithm" /> object used to create a digest.</param>
      <returns>The digest associated with an <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigC14NTransform.GetInnerXml">
      <summary>Returns an XML representation of the parameters of an <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object that are suitable to be included as subelements of an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</summary>
      <returns>A list of the XML nodes that represent the transform-specific content needed to describe the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object in an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigC14NTransform.GetOutput">
      <summary>Returns the output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object.</summary>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigC14NTransform.GetOutput(System.Type)">
      <summary>Returns the output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object of type <see cref="T:System.IO.Stream" />.</summary>
      <param name="type">The type of the output to return. <see cref="T:System.IO.Stream" /> is the only valid type for this parameter.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="type" /> parameter is not a <see cref="T:System.IO.Stream" /> object.</exception>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object of type <see cref="T:System.IO.Stream" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigC14NTransform.LoadInnerXml(System.Xml.XmlNodeList)">
      <summary>Parses the specified <see cref="T:System.Xml.XmlNodeList" /> object as transform-specific content of a <see langword="&lt;Transform&gt;" /> element; this method is not supported because this element has no inner XML elements.</summary>
      <param name="nodeList">An <see cref="T:System.Xml.XmlNodeList" /> object to load into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigC14NTransform.LoadInput(System.Object)">
      <summary>Loads the specified input into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object.</summary>
      <param name="obj">The input to load into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="obj" /> parameter is a <see cref="T:System.IO.Stream" /> object and it is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDsigC14NTransform.InputTypes">
      <summary>Gets an array of types that are valid inputs to the <see cref="M:System.Security.Cryptography.Xml.XmlDsigC14NTransform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object.</summary>
      <returns>An array of valid input types for the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object; you can pass only objects of one of these types to the <see cref="M:System.Security.Cryptography.Xml.XmlDsigC14NTransform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDsigC14NTransform.OutputTypes">
      <summary>Gets an array of types that are possible outputs from the <see cref="M:System.Security.Cryptography.Xml.XmlDsigC14NTransform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object.</summary>
      <returns>An array of valid output types for the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object; the <see cref="M:System.Security.Cryptography.Xml.XmlDsigC14NTransform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NTransform" /> object return only objects of one of these types.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.XmlDsigC14NWithCommentsTransform">
      <summary>Represents the C14N XML canonicalization transform for a digital signature as defined by the World Wide Web Consortium (W3C), with comments.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigC14NWithCommentsTransform.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigC14NWithCommentsTransform" /> class.</summary>
    </member>
    <member name="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform">
      <summary>Represents the enveloped signature transform for an XML digital signature as defined by the W3C.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> class with comments, if specified.</summary>
      <param name="includeComments">
        <see langword="true" /> to include comments; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform.GetInnerXml">
      <summary>Returns an XML representation of the parameters of an <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object that are suitable to be included as subelements of an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</summary>
      <returns>A list of the XML nodes that represent the transform-specific content needed to describe the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object in an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform.GetOutput">
      <summary>Returns the output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The containing XML document is <see langword="null" />.</exception>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform.GetOutput(System.Type)">
      <summary>Returns the output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object of type <see cref="T:System.Xml.XmlNodeList" />.</summary>
      <param name="type">The type of the output to return. <see cref="T:System.Xml.XmlNodeList" /> is the only valid type for this parameter.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="type" /> parameter is not an <see cref="T:System.Xml.XmlNodeList" /> object.</exception>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object of type <see cref="T:System.Xml.XmlNodeList" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform.LoadInnerXml(System.Xml.XmlNodeList)">
      <summary>Parses the specified <see cref="T:System.Xml.XmlNodeList" /> as transform-specific content of a <see langword="&lt;Transform&gt;" /> element and configures the internal state of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object to match the <see langword="&lt;Transform&gt;" /> element.</summary>
      <param name="nodeList">An <see cref="T:System.Xml.XmlNodeList" /> to load into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform.LoadInput(System.Object)">
      <summary>Loads the specified input into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object.</summary>
      <param name="obj">The input to load into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="obj" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The containing XML document is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform.InputTypes">
      <summary>Gets an array of types that are valid inputs to the <see cref="M:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object.</summary>
      <returns>An array of valid input types for the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object; you can pass only objects of one of these types to the <see cref="M:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform.OutputTypes">
      <summary>Gets an array of types that are possible outputs from the <see cref="M:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object.</summary>
      <returns>An array of valid output types for the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object; only objects of one of these types are returned from the <see cref="M:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigEnvelopedSignatureTransform" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform">
      <summary>Represents the exclusive C14N XML canonicalization transform for a digital signature as defined by the World Wide Web Consortium (W3C), without comments.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> class specifying a value that determines whether to include comments.</summary>
      <param name="includeComments">
        <see langword="true" /> to include comments; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.#ctor(System.Boolean,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> class specifying whether to include comments, and specifying a list of namespace prefixes.</summary>
      <param name="includeComments">
        <see langword="true" /> to include comments; otherwise, <see langword="false" />.</param>
      <param name="inclusiveNamespacesPrefixList">The namespace prefixes to canonicalize using the standard canonicalization algorithm.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> class specifying a list of namespace prefixes to canonicalize using the standard canonicalization algorithm.</summary>
      <param name="inclusiveNamespacesPrefixList">The namespace prefixes to canonicalize using the standard canonicalization algorithm.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.GetDigestedOutput(System.Security.Cryptography.HashAlgorithm)">
      <summary>Returns the digest associated with a <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object.</summary>
      <param name="hash">The <see cref="T:System.Security.Cryptography.HashAlgorithm" /> object used to create a digest.</param>
      <returns>The digest associated with a <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.GetInnerXml">
      <summary>Returns an XML representation of the parameters of a <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object that are suitable to be included as subelements of an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</summary>
      <returns>A list of the XML nodes that represent the transform-specific content needed to describe the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object in an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.GetOutput">
      <summary>Returns the output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object.</summary>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.GetOutput(System.Type)">
      <summary>Returns the output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object as an object of the specified type.</summary>
      <param name="type">The type of the output to return. This must be one of the types in the <see cref="P:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.OutputTypes" /> property.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="type" /> parameter is not a <see cref="T:System.IO.Stream" /> object.  
  
 -or-  
  
 The <paramref name="type" /> parameter does not derive from a <see cref="T:System.IO.Stream" /> object.</exception>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object as an object of the specified type.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.LoadInnerXml(System.Xml.XmlNodeList)">
      <summary>Parses the specified <see cref="T:System.Xml.XmlNodeList" /> object as transform-specific content of a <see langword="&lt;Transform&gt;" /> element and configures the internal state of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object to match the <see langword="&lt;Transform&gt;" /> element.</summary>
      <param name="nodeList">An <see cref="T:System.Xml.XmlNodeList" /> object that specifies transform-specific content for the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.LoadInput(System.Object)">
      <summary>When overridden in a derived class, loads the specified input into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object.</summary>
      <param name="obj">The input to load into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="obj" /> parameter is not a <see cref="T:System.IO.Stream" /> object.  
  
 -or-  
  
 The <paramref name="obj" /> parameter is not an <see cref="T:System.Xml.XmlDocument" /> object.  
  
 -or-  
  
 The <paramref name="obj" /> parameter is not an <see cref="T:System.Xml.XmlNodeList" /> object.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.InclusiveNamespacesPrefixList">
      <summary>Gets or sets a string that contains namespace prefixes to canonicalize using the standard canonicalization algorithm.</summary>
      <returns>A string that contains namespace prefixes to canonicalize using the standard canonicalization algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.InputTypes">
      <summary>Gets an array of types that are valid inputs to the <see cref="M:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object.</summary>
      <returns>An array of valid input types for the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object; you can pass only objects of one of these types to the <see cref="M:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.OutputTypes">
      <summary>Gets an array of types that are possible outputs from the <see cref="M:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object.</summary>
      <returns>An array of valid output types for the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object; the <see cref="Overload:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NTransform" /> object return only objects of one of these types.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.XmlDsigExcC14NWithCommentsTransform">
      <summary>Represents the exclusive C14N XML canonicalization transform for a digital signature as defined by the World Wide Web Consortium (W3C), with comments.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigExcC14NWithCommentsTransform.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NWithCommentsTransform" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigExcC14NWithCommentsTransform.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigExcC14NWithCommentsTransform" /> class specifying a list of namespace prefixes to canonicalize using the standard canonicalization algorithm.</summary>
      <param name="inclusiveNamespacesPrefixList">The namespace prefixes to canonicalize using the standard canonicalization algorithm.</param>
    </member>
    <member name="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform">
      <summary>Represents the XPath transform for a digital signature as defined by the W3C.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigXPathTransform.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigXPathTransform.GetInnerXml">
      <summary>Returns an XML representation of the parameters of a <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object that are suitable to be included as subelements of an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</summary>
      <returns>A list of the XML nodes that represent the transform-specific content needed to describe the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object in an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigXPathTransform.GetOutput">
      <summary>Returns the output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object.</summary>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigXPathTransform.GetOutput(System.Type)">
      <summary>Returns the output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object of type <see cref="T:System.Xml.XmlNodeList" />.</summary>
      <param name="type">The type of the output to return. <see cref="T:System.Xml.XmlNodeList" /> is the only valid type for this parameter.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="type" /> parameter is not an <see cref="T:System.Xml.XmlNodeList" /> object.</exception>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object of type <see cref="T:System.Xml.XmlNodeList" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigXPathTransform.LoadInnerXml(System.Xml.XmlNodeList)">
      <summary>Parses the specified <see cref="T:System.Xml.XmlNodeList" /> object as transform-specific content of a <see langword="&lt;Transform&gt;" /> element and configures the internal state of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object to match the <see langword="&lt;Transform&gt;" /> element.</summary>
      <param name="nodeList">An <see cref="T:System.Xml.XmlNodeList" /> object to load into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="nodeList" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The <paramref name="nodeList" /> parameter does not contain an <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> element.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigXPathTransform.LoadInput(System.Object)">
      <summary>Loads the specified input into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object.</summary>
      <param name="obj">The input to load into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object.</param>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDsigXPathTransform.InputTypes">
      <summary>Gets an array of types that are valid inputs to the <see cref="M:System.Security.Cryptography.Xml.XmlDsigXPathTransform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object.</summary>
      <returns>An array of valid input types for the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object; you can pass only objects of one of these types to the <see cref="M:System.Security.Cryptography.Xml.XmlDsigXPathTransform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDsigXPathTransform.OutputTypes">
      <summary>Gets an array of types that are possible outputs from the <see cref="M:System.Security.Cryptography.Xml.XmlDsigXPathTransform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object.</summary>
      <returns>An array of valid output types for the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object; the <see cref="M:System.Security.Cryptography.Xml.XmlDsigXPathTransform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXPathTransform" /> object return only objects of one of these types.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform">
      <summary>Represents the XSLT transform for a digital signature as defined by the W3C.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigXsltTransform.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigXsltTransform.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> class with comments, if specified.</summary>
      <param name="includeComments">
        <see langword="true" /> to include comments; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigXsltTransform.GetInnerXml">
      <summary>Returns an XML representation of the parameters of the <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object that are suitable to be included as subelements of an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</summary>
      <returns>A list of the XML nodes that represent the transform-specific content needed to describe the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object in an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigXsltTransform.GetOutput">
      <summary>Returns the output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object.</summary>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigXsltTransform.GetOutput(System.Type)">
      <summary>Returns the output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object of type <see cref="T:System.IO.Stream" />.</summary>
      <param name="type">The type of the output to return. <see cref="T:System.IO.Stream" /> is the only valid type for this parameter.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="type" /> parameter is not a <see cref="T:System.IO.Stream" /> object.</exception>
      <returns>The output of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object of type <see cref="T:System.IO.Stream" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigXsltTransform.LoadInnerXml(System.Xml.XmlNodeList)">
      <summary>Parses the specified <see cref="T:System.Xml.XmlNodeList" /> object as transform-specific content of a <see langword="&lt;Transform&gt;" /> element and configures the internal state of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object to match the <see langword="&lt;Transform&gt;" /> element.</summary>
      <param name="nodeList">An <see cref="T:System.Xml.XmlNodeList" /> object that encapsulates an XSLT style sheet to load into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object. This style sheet is applied to the document loaded by the <see cref="M:System.Security.Cryptography.Xml.XmlDsigXsltTransform.LoadInput(System.Object)" /> method.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="nodeList" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The <paramref name="nodeList" /> parameter does not contain an <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlDsigXsltTransform.LoadInput(System.Object)">
      <summary>Loads the specified input into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object.</summary>
      <param name="obj">The input to load into the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object.</param>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDsigXsltTransform.InputTypes">
      <summary>Gets an array of types that are valid inputs to the <see cref="M:System.Security.Cryptography.Xml.XmlDsigXsltTransform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object.</summary>
      <returns>An array of valid input types for the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object; you can pass only objects of one of these types to the <see cref="M:System.Security.Cryptography.Xml.XmlDsigXsltTransform.LoadInput(System.Object)" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlDsigXsltTransform.OutputTypes">
      <summary>Gets an array of types that are possible outputs from the <see cref="M:System.Security.Cryptography.Xml.XmlDsigXsltTransform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object.</summary>
      <returns>An array of valid output types for the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object; only objects of one of these types are returned from the <see cref="M:System.Security.Cryptography.Xml.XmlDsigXsltTransform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlDsigXsltTransform" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Xml.XmlLicenseTransform">
      <summary>Represents the license transform algorithm used to normalize XrML licenses for signatures.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlLicenseTransform.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlLicenseTransform.GetInnerXml">
      <summary>Returns an XML representation of the parameters of an <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object that are suitable to be included as subelements of an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</summary>
      <returns>A list of the XML nodes that represent the transform-specific content needed to describe the current <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object in an XMLDSIG <see langword="&lt;Transform&gt;" /> element.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlLicenseTransform.GetOutput">
      <summary>Returns the output of an <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object.</summary>
      <returns>The output of the <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlLicenseTransform.GetOutput(System.Type)">
      <summary>Returns the output of an <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object.</summary>
      <param name="type">The type of the output to return. <see cref="T:System.Xml.XmlDocument" /> is the only valid type for this parameter.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="type" /> parameter is not an <see cref="T:System.Xml.XmlDocument" /> object.</exception>
      <returns>The output of the <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlLicenseTransform.LoadInnerXml(System.Xml.XmlNodeList)">
      <summary>Parses the specified <see cref="T:System.Xml.XmlNodeList" /> object as transform-specific content of a <see langword="&lt;Transform&gt;" /> element; this method is not supported because the <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object has no inner XML elements.</summary>
      <param name="nodeList">An <see cref="T:System.Xml.XmlNodeList" /> object that encapsulates the transform to load into the current <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.Xml.XmlLicenseTransform.LoadInput(System.Object)">
      <summary>Loads the specified input into the current <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object.</summary>
      <param name="obj">The input to load into the current <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object. The type of the input object must be <see cref="T:System.Xml.XmlDocument" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The context was not set before this transform was invoked.  
  
 -or-  
  
 The <see langword="&lt;issuer&gt;" /> element was not set before this transform was invoked.  
  
 -or-  
  
 The <see langword="&lt;license&gt;" /> element was not set before this transform was invoked.  
  
 -or-  
  
 The <see cref="P:System.Security.Cryptography.Xml.XmlLicenseTransform.Decryptor" /> property was not set before this transform was invoked.</exception>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlLicenseTransform.Decryptor">
      <summary>Gets or sets the decryptor of the current <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object.</summary>
      <returns>The decryptor of the current <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlLicenseTransform.InputTypes">
      <summary>Gets an array of types that are valid inputs to the <see cref="P:System.Security.Cryptography.Xml.XmlLicenseTransform.OutputTypes" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object.</summary>
      <returns>An array of types that are valid inputs to the <see cref="P:System.Security.Cryptography.Xml.XmlLicenseTransform.OutputTypes" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object; you can pass only objects of one of these types to the <see cref="P:System.Security.Cryptography.Xml.XmlLicenseTransform.OutputTypes" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Xml.XmlLicenseTransform.OutputTypes">
      <summary>Gets an array of types that are valid outputs from the <see cref="P:System.Security.Cryptography.Xml.XmlLicenseTransform.OutputTypes" /> method of the current <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object.</summary>
      <returns>An array of valid output types for the current <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object; only objects of one of these types are returned from the <see cref="M:System.Security.Cryptography.Xml.XmlLicenseTransform.GetOutput" /> methods of the current <see cref="T:System.Security.Cryptography.Xml.XmlLicenseTransform" /> object.</returns>
    </member>
  </members>
</doc>