<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{FF4F68C6-3711-41D1-AC4F-CA4BA26D01FF}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RainWorx.FrameWorx.MVC</RootNamespace>
    <AssemblyName>RainWorx.FrameWorx.MVC</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort>44302</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication>enabled</IISExpressAnonymousAuthentication>
    <IISExpressWindowsAuthentication>disabled</IISExpressWindowsAuthentication>
    <IISExpressUseClassicPipelineMode>false</IISExpressUseClassicPipelineMode>
    <TargetFrameworkProfile />
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <UseGlobalApplicationHostFile />
    <Use64BitIISExpress />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\RainWorx.FrameWorx.MVC.XML</DocumentationFile>
    <NoWarn>
    </NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>
    </DocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Antlr3.Runtime, Version=3.5.0.2, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper, Version=3.1.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>bin\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper.Net4, Version=3.1.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>bin\AutoMapper.Net4.dll</HintPath>
    </Reference>
    <Reference Include="BrockAllen.CookieTempData, Version=1.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>bin\BrockAllen.CookieTempData.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="CsvHelper, Version=30.0.0.0, Culture=neutral, PublicKeyToken=8c4959082be5c823, processorArchitecture=MSIL">
      <HintPath>packages\CsvHelper.30.0.1\lib\net47\CsvHelper.dll</HintPath>
    </Reference>
    <Reference Include="DuoVia.FuzzyStrings, Version=2.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\DuoVia.FuzzyStrings.2.1.0\lib\net462\DuoVia.FuzzyStrings.dll</HintPath>
    </Reference>
    <Reference Include="EasyPost, Version=5.1.0.0, Culture=neutral, PublicKeyToken=87e695d461b6704b, processorArchitecture=MSIL">
      <HintPath>packages\EasyPost-Official.5.1.0\lib\netstandard2.0\EasyPost.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>bin\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>bin\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus, Version=8.1.1.0, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>packages\EPPlus.8.1.1\lib\net462\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus.Interfaces, Version=*******, Culture=neutral, PublicKeyToken=a694d7f3b0907a61, processorArchitecture=MSIL">
      <HintPath>packages\EPPlus.Interfaces.8.1.0\lib\net462\EPPlus.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis, Version=********, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>packages\Google.Apis.1.68.0\lib\net462\Google.Apis.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.AddressValidation.v1, Version=1.68.0.3471, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>packages\Google.Apis.AddressValidation.v1.1.68.0.3471\lib\net462\Google.Apis.AddressValidation.v1.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Auth, Version=********, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>packages\Google.Apis.Auth.1.68.0\lib\net462\Google.Apis.Auth.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.Core, Version=********, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>packages\Google.Apis.Core.1.68.0\lib\net462\Google.Apis.Core.dll</HintPath>
    </Reference>
    <Reference Include="Google.Apis.ShoppingContent.v2_1, Version=1.64.0.3258, Culture=neutral, PublicKeyToken=4b01fa6e34db77ab, processorArchitecture=MSIL">
      <HintPath>packages\Google.Apis.ShoppingContent.v2_1.1.64.0.3258\lib\net45\Google.Apis.ShoppingContent.v2_1.dll</HintPath>
    </Reference>
    <Reference Include="GraphQL.Client, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\GraphQL.Client.6.1.0\lib\net461\GraphQL.Client.dll</HintPath>
    </Reference>
    <Reference Include="GraphQL.Client.Abstractions, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\GraphQL.Client.Abstractions.6.1.0\lib\netstandard2.0\GraphQL.Client.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="GraphQL.Client.Abstractions.Websocket, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\GraphQL.Client.Abstractions.Websocket.6.1.0\lib\netstandard2.0\GraphQL.Client.Abstractions.Websocket.dll</HintPath>
    </Reference>
    <Reference Include="GraphQL.Client.Serializer.Newtonsoft, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\GraphQL.Client.Serializer.Newtonsoft.6.1.0\lib\netstandard2.0\GraphQL.Client.Serializer.Newtonsoft.dll</HintPath>
    </Reference>
    <Reference Include="GraphQL.Primitives, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\GraphQL.Primitives.6.1.0\lib\netstandard2.0\GraphQL.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="HtmlAgilityPack, Version=1.6.7.0, Culture=neutral, PublicKeyToken=bd319b19eaf3b43a, processorArchitecture=MSIL">
      <HintPath>bin\HtmlAgilityPack.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.EntityFramework, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.AspNet.Identity.EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Owin, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Microsoft.AspNet.Identity.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Core, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.AspNet.SignalR.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.ServiceBus3, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.AspNet.SignalR.ServiceBus3.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.SqlServer, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.AspNet.SignalR.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.SystemWeb, Version=2.4.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.AspNet.SignalR.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.WebUtilities, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.WebUtilities.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.WebUtilities.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.KeyVault.Core, Version=3.0.5.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Azure.KeyVault.Core.3.0.5\lib\net461\Microsoft.Azure.KeyVault.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.HashCode, Version=1.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Bcl.HashCode.1.0.0\lib\net461\Microsoft.Bcl.HashCode.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Data.Edm, Version=5.8.5.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Data.Edm.5.8.5\lib\net40\Microsoft.Data.Edm.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Data.OData, Version=5.8.5.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Data.OData.5.8.5\lib\net40\Microsoft.Data.OData.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Data.Services.Client, Version=5.8.5.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Data.Services.Client.5.8.5\lib\net40\Microsoft.Data.Services.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=2.1.1.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Logging.Abstractions.2.1.1\lib\netstandard2.0\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Primitives.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Abstractions, Version=6.36.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.IdentityModel.Abstractions.6.36.0\lib\net472\Microsoft.IdentityModel.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens, Version=6.36.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.IdentityModel.JsonWebTokens.6.36.0\lib\net472\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=6.36.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.IdentityModel.Logging.6.36.0\lib\net472\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=6.36.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.IdentityModel.Tokens.6.36.0\lib\net472\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IO.RecyclableMemoryStream, Version=3.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.IO.RecyclableMemoryStream.3.0.1\lib\netstandard2.0\Microsoft.IO.RecyclableMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Net.Http.Headers, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Net.Http.Headers.2.2.0\lib\netstandard2.0\Microsoft.Net.Http.Headers.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.Owin.Security.Cookies.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Facebook, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.Owin.Security.Facebook.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Google, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.Owin.Security.Google.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.Owin.Security.OAuth.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Common, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Microsoft.Practices.EnterpriseLibrary.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Validation, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Microsoft.Practices.EnterpriseLibrary.Validation.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.ServiceLocation, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Microsoft.Practices.ServiceLocation.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.Practices.Unity.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Configuration, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.Practices.Unity.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Interception, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.Practices.Unity.Interception.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Interception.Configuration, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.Practices.Unity.Interception.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Rest.ClientRuntime, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Rest.ClientRuntime.2.3.24\lib\net461\Microsoft.Rest.ClientRuntime.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Rest.ClientRuntime.Azure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Rest.ClientRuntime.Azure.3.3.18\lib\net452\Microsoft.Rest.ClientRuntime.Azure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ServiceBus, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\Microsoft.ServiceBus.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Storage, Version=8.6.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\WindowsAzure.Storage.8.6.0\lib\net45\Microsoft.WindowsAzure.Storage.dll</HintPath>
    </Reference>
    <Reference Include="Mindscape.LightSpeed, Version=4.0.1534.21185, Culture=neutral, PublicKeyToken=360c8f37b466ebb2, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Mindscape.LightSpeed.dll</HintPath>
    </Reference>
    <Reference Include="Mindscape.LightSpeed.Linq, Version=4.0.1534.21185, Culture=neutral, PublicKeyToken=360c8f37b466ebb2, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Mindscape.LightSpeed.Linq.dll</HintPath>
    </Reference>
    <Reference Include="MvcSiteMap.Core">
      <HintPath>bin\MvcSiteMap.Core.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=1*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Owin">
      <HintPath>bin\Owin.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="RainWorx.FrameWorx.Providers.ConsignmentLogic, Version=3.5.8158.37322, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.ConsignmentLogic.dll</HintPath>
    </Reference>
    <Reference Include="Recaptcha.Web">
      <HintPath>bin\Recaptcha.Web.dll</HintPath>
    </Reference>
    <Reference Include="Stripe.net, Version=*********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>bin\Stripe.net.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.CodeDom, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.CodeDom.7.0.0\lib\net462\System.CodeDom.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.ComponentModel.Annotations.5.0.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Data.Services.Client" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=4.0.5.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Diagnostics.DiagnosticSource.4.7.1\lib\net46\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.DirectoryServices.AccountManagement" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=6.36.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\System.IdentityModel.Tokens.Jwt.6.36.0\lib\net472\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http.Extensions, Version=2.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>bin\System.Net.Http.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\System.Net.Http.Formatting.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.Primitives, Version=4.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>bin\System.Net.Http.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client.Managed, Version=1.0.22.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\System.Net.WebSockets.Client.Managed.1.0.22\lib\net45\System.Net.WebSockets.Client.Managed.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reactive, Version=6.0.0.0, Culture=neutral, PublicKeyToken=94bc3704cddfc263, processorArchitecture=MSIL">
      <HintPath>packages\System.Reactive.6.0.0\lib\net472\System.Reactive.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime" />
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>bin\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Remoting" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Cryptography.Xml, Version=8.0.0.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Cryptography.Xml.8.0.2\lib\net462\System.Security.Cryptography.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Spatial, Version=5.8.5.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\System.Spatial.5.8.5\lib\net40\System.Spatial.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Text.Encodings.Web.8.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=8.0.0.5, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Text.Json.8.0.5\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\System.Web.Cors.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\System.Web.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\System.Web.Http.Cors.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http.OData, Version=5.7.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\System.Web.Http.OData.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\System.Web.Http.Owin.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Tracing, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\System.Web.Http.Tracing.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNet.Mvc.5.3.0\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNet.Razor.3.3.0\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNet.WebPages.3.3.0\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Net.Http">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Twilio, Version=6.18.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\Twilio.6.18.0\lib\net451\Twilio.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease, Version=1.6.5135.21930, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="WebMatrix.Data, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\WebMatrix.Data.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="WebMatrix.WebData, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>bin\WebMatrix.WebData.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="netstandard">
      <Private>True</Private>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.BLL.Events">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.BLL.Events.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.BLL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.BLL.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Clients">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Clients.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.DAL.LightSpeed.Logger">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.DAL.LightSpeed.Logger.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.DAL.LightSpeed">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.DAL.LightSpeed.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.DAL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.DAL.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.DTO">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.DTO.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Filters">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Filters.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Imaging">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Imaging.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Policies">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Policies.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.AuthorizationToken">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.AuthorizationToken.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Fee.Standard">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Fee.Standard.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Fee">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Fee.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Listing.Auction">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Listing.Auction.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Listing.Classified">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Listing.Classified.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Listing.FixedPrice">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Listing.FixedPrice.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Listing">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Listing.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Media.Azure">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Media.Azure.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Media.YouTube">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Media.YouTube.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.MediaAsset.Files">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.MediaAsset.Files.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.MediaAsset.HTML">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.MediaAsset.HTML.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.MediaAsset.Images">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.MediaAsset.Images.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.MediaAsset">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.MediaAsset.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.MediaLoader.URI">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.MediaLoader.URI.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.MediaLoader">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.MediaLoader.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.MediaSaver.FileSystem">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.MediaSaver.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.MediaSaver">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.MediaSaver.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Notification">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Notification.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.NotifierTemplates.FileSystem">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.NotifierTemplates.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Payment.AuthorizeNet">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Payment.AuthorizeNet.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Payment.Dummy">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Payment.Dummy.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Payment.FreeForm">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Payment.FreeForm.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Payment.PayPal">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Payment.PayPal.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Payment.Stripe">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Payment.Stripe.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Providers.Payment">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Providers.Payment.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Queueing">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Queueing.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Services">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Services.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.SiteMap">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.SiteMap.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Strings">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Strings.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Unity">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Unity.dll</HintPath>
    </Reference>
    <Reference Include="RainWorx.FrameWorx.Utility">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RainWorx.FrameWorx.Utility.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_GlobalResources\AdminStrings.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>AdminStrings.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\AriaStrings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>AriaStrings.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\AuctionListing.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>AuctionListing.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\CategoriesRegions.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CategoriesRegions.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\ClassifiedListing.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ClassifiedListing.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\Countries.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Countries.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\CustomFields.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CustomFields.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\FixedPriceListing.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>FixedPriceListing.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\GlobalStrings.designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>GlobalStrings.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\Licensing.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Licensing.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\ShippingOptions.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ShippingOptions.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\States.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>States.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\TimeZones.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>TimeZones.resx</DependentUpon>
    </Compile>
    <Compile Include="App_GlobalResources\Validation.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Validation.resx</DependentUpon>
    </Compile>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\IdentityConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\Startup.Auth.cs" />
    <Compile Include="App_Start\Startup.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Areas\API\APIAreaRegistration.cs" />
    <Compile Include="Areas\API\Controllers\EventController.cs" />
    <Compile Include="Areas\API\Controllers\SettingsController.cs" />
    <Compile Include="Areas\API\Controllers\SystemController.cs" />
    <Compile Include="Areas\API\Controllers\Base\AuctionWorxAPIController.cs" />
    <Compile Include="Areas\API\Controllers\CategoryController.cs" />
    <Compile Include="Areas\API\Controllers\CustomFieldController.cs" />
    <Compile Include="Areas\API\Controllers\Helpers\Helpers.cs" />
    <Compile Include="Areas\API\Controllers\Test\TestSettingsController.cs" />
    <Compile Include="Areas\API\Controllers\Test\TestUserController.cs" />
    <Compile Include="Areas\API\Controllers\UserController.cs" />
    <Compile Include="Areas\API\MessageHandlers\APIEnabled.cs" />
    <Compile Include="Areas\API\MessageHandlers\BasicSignRequest.cs" />
    <Compile Include="Areas\API\MessageHandlers\BasicVerifySignature.cs" />
    <Compile Include="Areas\API\Models\APIEvent.cs" />
    <Compile Include="Areas\API\Models\ListingActionPostResponse.cs" />
    <Compile Include="Areas\API\Controllers\Test\TestSystemController.cs" />
    <Compile Include="Areas\API\Controllers\Test\TestController.cs" />
    <Compile Include="Areas\API\Controllers\ListingActionController.cs" />
    <Compile Include="Areas\API\Controllers\ListingController.cs" />
    <Compile Include="Areas\API\Controllers\MediaController.cs" />
    <Compile Include="Areas\API\Controllers\Test\TestCategoryController.cs" />
    <Compile Include="Areas\API\Controllers\Base\TestControllerBase.cs" />
    <Compile Include="Areas\API\Controllers\Test\TestCustomFieldController.cs" />
    <Compile Include="Areas\API\Controllers\Test\TestListingActionController.cs" />
    <Compile Include="Areas\API\Controllers\Test\TestListingController.cs" />
    <Compile Include="Areas\API\Controllers\Test\TestMediaController.cs" />
    <Compile Include="Areas\API\Controllers\Test\TestUseCaseController.cs" />
    <Compile Include="Areas\API\Filters\UnhandledExceptionFilter.cs" />
    <Compile Include="Areas\API\MessageHandlers\OverrideDate.cs" />
    <Compile Include="Areas\API\MessageHandlers\OverrideMethod.cs" />
    <Compile Include="Areas\API\MessageHandlers\SecureSignRequest.cs" />
    <Compile Include="Areas\API\MessageHandlers\SecureVerifySignature.cs" />
    <Compile Include="Areas\API\Models\AccountLoginRequest.cs" />
    <Compile Include="Areas\API\Models\CustomFieldAssignRequest.cs" />
    <Compile Include="Areas\API\Models\CustomFieldPostEnumerationRequest.cs" />
    <Compile Include="Areas\API\Models\ErrorResponse.cs" />
    <Compile Include="Areas\API\Models\APIListing.cs" />
    <Compile Include="Areas\API\Models\MediaPostStringRequest.cs" />
    <Compile Include="Areas\API\Models\MediaPostURIRequest.cs" />
    <Compile Include="Areas\API\Models\APIUser.cs" />
    <Compile Include="Areas\API\Utilities.cs" />
    <Compile Include="Areas\HelpPage\ApiDescriptionExtensions.cs" />
    <Compile Include="Areas\HelpPage\App_Start\HelpPageConfig.cs" />
    <Compile Include="Areas\HelpPage\Controllers\HelpController.cs" />
    <Compile Include="Areas\HelpPage\HelpPageAreaRegistration.cs" />
    <Compile Include="Areas\HelpPage\HelpPageConfigurationExtensions.cs" />
    <Compile Include="Areas\HelpPage\Models\HelpPageApiModel.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\HelpPageSampleGenerator.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\HelpPageSampleKey.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\ImageSample.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\InvalidSample.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\ObjectGenerator.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\SampleDirection.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\TextSample.cs" />
    <Compile Include="Areas\HelpPage\XmlDocumentationProvider.cs" />
    <Compile Include="Controllers\AccountController.cs" />
    <Compile Include="Controllers\AdminController.cs" />
    <Compile Include="Controllers\AuctionWorxController.cs" />
    <Compile Include="Controllers\EventController.cs" />
    <Compile Include="Controllers\ShipStationController.cs" />
    <Compile Include="Controllers\WarmupController.cs" />
    <Compile Include="Controllers\StripeController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\ListingController.cs" />
    <Compile Include="Controllers\ListingHub.cs" />
    <Compile Include="Controllers\MediaController.cs" />
    <Compile Include="Controllers\PageController.cs" />
    <Compile Include="Controllers\RealTimeController.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Helpers\GoogleAddressValidation.cs" />
    <Compile Include="Helpers\GoogleShopping\ShoppingUtil.cs" />
    <Compile Include="Helpers\GoogleShopping\ProductsUtil.cs" />
    <Compile Include="Helpers\InternationalTaxAndShippingService.cs" />
    <Compile Include="Helpers\InvoiceHelper.cs" />
    <Compile Include="Helpers\ITaxAndShippingService.cs" />
    <Compile Include="Helpers\LoggingHandler.cs" />
    <Compile Include="Helpers\Currency.cs" />
    <Compile Include="Helpers\MagnoliapearlStringConstants.cs" />
    <Compile Include="Helpers\HtmlHelpers.cs" />
    <Compile Include="Helpers\CommonHelper.cs" />
    <Compile Include="Helpers\Localization.cs" />
    <Compile Include="Helpers\MVCTransferResult.cs" />
    <Compile Include="Helpers\RecaptchaMVC.cs" />
    <Compile Include="Helpers\ShipStationClient.cs" />
    <Compile Include="Helpers\SMSClientHelpers.cs" />
    <Compile Include="Helpers\StripeHelpers.cs" />
    <Compile Include="Helpers\ResourceExtensions.cs" />
    <Compile Include="Helpers\UsTaxAndShippingService.cs" />
    <Compile Include="Helpers\ZonosClient.cs" />
    <Compile Include="IPN.aspx.cs">
      <DependentUpon>IPN.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="IPN.aspx.designer.cs">
      <DependentUpon>IPN.aspx</DependentUpon>
    </Compile>
    <Compile Include="IpnTester.aspx.cs">
      <DependentUpon>IpnTester.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="IpnTester.aspx.designer.cs">
      <DependentUpon>IpnTester.aspx</DependentUpon>
    </Compile>
    <Compile Include="Models\Framework\RWX_Users.cs" />
    <Compile Include="Models\FuzzyMatchResult.cs" />
    <Compile Include="Models\CommissionReport.cs" />
    <Compile Include="Models\CommonHelperModel.cs" />
    <Compile Include="Models\Framework\aweFramework.cs" />
    <Compile Include="Models\Framework\GoogleProductMappings.cs" />
    <Compile Include="Models\Framework\RWX_Addresses.cs" />
    <Compile Include="Models\Framework\RWX_Listings.cs" />
    <Compile Include="Models\Framework\Refunds.cs" />
    <Compile Include="Models\Framework\RWX_Notifications.cs" />
    <Compile Include="Models\Framework\RWX_ShippingMethods.cs" />
    <Compile Include="Models\Framework\RWX_PaymentResponses.cs" />
    <Compile Include="Models\Framework\RWX_Invoices.cs" />
    <Compile Include="Models\Framework\RWX_ShippingOptions.cs" />
    <Compile Include="Models\Framework\RWX_SliderImage.cs" />
    <Compile Include="Models\Framework\RWX_CategoryImage.cs" />
    <Compile Include="Models\Google\PostalAddress.cs" />
    <Compile Include="Models\NewsLetterModels.cs" />
    <Compile Include="Models\AccountViewModels.cs" />
    <Compile Include="Models\ControllerAttributes.cs" />
    <Compile Include="Models\CreditCardWithBillingAddress.cs" />
    <Compile Include="Models\CSV\CategoryColumnSpec.cs" />
    <Compile Include="Models\CSV\ColumnSpecBase.cs" />
    <Compile Include="Models\CSV\CSV.cs" />
    <Compile Include="Models\CSV\CurrencyColumnSpec.cs" />
    <Compile Include="Models\CSV\CustomFieldColumnSpec.cs" />
    <Compile Include="Models\CSV\DateTimeColumnSpec.cs" />
    <Compile Include="Models\CSV\DecorationColumnSpec.cs" />
    <Compile Include="Models\CSV\IColumnSpec.cs" />
    <Compile Include="Models\CSV\PdfColumnSpec.cs" />
    <Compile Include="Models\CSV\ImageColumnSpec.cs" />
    <Compile Include="Models\CSV\ImportData.cs" />
    <Compile Include="Models\CSV\ImportListing.cs" />
    <Compile Include="Models\CSV\MakeOfferColumnSpec.cs" />
    <Compile Include="Models\CSV\PriceColumnSpec.cs" />
    <Compile Include="Models\CSV\ConsignorColumnSpec.cs" />
    <Compile Include="Models\EventLogStat.cs" />
    <Compile Include="Models\IdentityModels.cs" />
    <Compile Include="Models\KeepAlive.cs" />
    <Compile Include="Models\CSV\ListingTypeColumnSpec.cs" />
    <Compile Include="Models\CSV\LocationColumnSpec.cs" />
    <Compile Include="Models\CSV\RegionColumnSpec.cs" />
    <Compile Include="Models\CSV\ShippingColumnSpec.cs" />
    <Compile Include="Models\CSV\SimpleColumnSpec.cs" />
    <Compile Include="Models\CSV\UserColumnSpec.cs" />
    <Compile Include="Models\CSV\YouTubeColumnSpec.cs" />
    <Compile Include="Models\CustomFieldComparer.cs" />
    <Compile Include="Models\DiskFileStore.cs" />
    <Compile Include="Models\EventFee.cs" />
    <Compile Include="Models\PartialViewData.cs" />
    <Compile Include="Models\QuerySortDefinitions.cs" />
    <Compile Include="Models\RequestModels\LineItemsRoute.cs" />
    <Compile Include="Models\RequestModels\RequestAddress.cs" />
    <Compile Include="Models\RequestModels\AvalaraSalesOrderRequest.cs" />
    <Compile Include="Models\RequestModels\ItemDetails.cs" />
    <Compile Include="Models\RequestModels\ShippingDetails.cs" />
    <Compile Include="Models\RwaCustomField.cs" />
    <Compile Include="Models\ShipStation\AdvancedOptions.cs" />
    <Compile Include="Models\ShipStation\BillTo.cs" />
    <Compile Include="Models\ShipStation\InsuranceOptions.cs" />
    <Compile Include="Models\ShipStation\Item.cs" />
    <Compile Include="Models\ShipStation\ShipStationOrder.cs" />
    <Compile Include="Models\ShipStation\ShipTo.cs" />
    <Compile Include="Models\SignalRLoggingModule.cs" />
    <Compile Include="Models\SiteCheck.cs" />
    <Compile Include="Models\SiteFeeAmounts.cs" />
    <Compile Include="Models\ReportColumn.cs" />
    <Compile Include="Models\TaxRate.cs" />
    <Compile Include="Models\Zonos\Shipping.cs" />
    <Compile Include="Models\Zonos\ShipTo.cs" />
    <Compile Include="Models\Zonos\Dimensions.cs" />
    <Compile Include="Models\Zonos\Discount.cs" />
    <Compile Include="Models\Zonos\Item.cs" />
    <Compile Include="Models\Zonos\ZonosLandedCost.cs" />
    <Compile Include="PaymentResult.aspx.cs">
      <DependentUpon>PaymentResult.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PaymentResult.aspx.designer.cs">
      <DependentUpon>PaymentResult.aspx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_Data\XmlDocument.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Areas\API\Content\css\Site.css" />
    <Content Include="Areas\HelpPage\HelpPage.css" />
    <Content Include="Content\AWE-bFlat.css" />
    <Content Include="Content\AWE-Default-Square.css" />
    <Content Include="Content\AWE-Demo.css" />
    <Content Include="Content\AWE-DropShadow.css" />
    <Content Include="Content\AWE-Gallery-Simple.css" />
    <Content Include="Content\AWE-Neutral.css" />
    <Content Include="Content\AWE-Solid-Black.css" />
    <Content Include="Content\AWE_Admin.css" />
    <Content Include="Content\AWE_Bootstrap3.css" />
    <Content Include="Content\AWE-Print.css" />
    <Content Include="Content\AWE_Boxi_Neutral_01.css" />
    <Content Include="Content\AWE_Galaxy_Gold.css" />
    <Content Include="Content\AWE_Galaxy_Silver.css" />
    <Content Include="Content\bootstrap-dialog.css" />
    <Content Include="Content\bootstrap-social.css" />
    <Content Include="Content\bootstrap-theme.css" />
    <Content Include="Content\bootstrap-theme.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\css\bootstrap-multiselect.css" />
    <Content Include="Content\css\custom.css" />
    <Content Include="Content\css\everslider.css" />
    <Content Include="Content\css\jquery.ptTimeSelect.css" />
    <Content Include="Content\css\jquery.timepickr.css" />
    <Content Include="Content\css\jquery.ui.stars.css" />
    <Content Include="Content\css\jquery.ui.stars.min.css" />
    <Content Include="Content\css\lightbox.css" />
    <Content Include="Content\css\reset.css" />
    <Content Include="Content\css\scrollup.css" />
    <Content Include="Content\css\stripe.css" />
    <Content Include="Content\font\font.css" />
    <Content Include="Content\font\stylesheet.css" />
    <Content Include="Content\Images\amazon.png" />
    <Content Include="Content\Images\Arrow-Icon.png" />
    <Content Include="Content\Images\auction1_img.png" />
    <Content Include="Content\Images\auction2_img.png" />
    <Content Include="Content\Images\auction3_img.png" />
    <Content Include="Content\Images\auction4_img.png" />
    <Content Include="Content\Images\auction5_img.png" />
    <Content Include="Content\Images\auction6_img.png" />
    <Content Include="Content\Images\auction7_img.png" />
    <Content Include="Content\Images\auctionBanner.png" />
    <Content Include="Content\Images\authentic.png" />
    <Content Include="Content\Images\bgerror.jpg" />
    <Content Include="Content\Images\blankButton.png" />
    <Content Include="Content\Images\Book-Menu-Tile-01_200x.jpg" />
    <Content Include="Content\Images\catname.png" />
    <Content Include="Content\Images\classifiedAdBanner.png" />
    <Content Include="Content\Images\coma.jpg" />
    <Content Include="Content\Images\coma.png" />
    <Content Include="Content\Images\comari.jpg" />
    <Content Include="Content\Images\comari.png" />
    <Content Include="Content\Images\contactlogo.png" />
    <Content Include="Content\Images\Email Field.png" />
    <Content Include="Content\Images\everslider\arrows%402x.png" />
    <Content Include="Content\Images\everslider\arrows.png" />
    <Content Include="Content\Images\everslider\bullets%402x.png" />
    <Content Include="Content\Images\everslider\bullets.png" />
    <Content Include="Content\Images\everslider\preload.gif" />
    <Content Include="Content\Images\everslider\ticker%402x.png" />
    <Content Include="Content\Images\everslider\ticker.png" />
    <Content Include="Content\Images\ExampleEventThumb.jpg" />
    <Content Include="Content\Images\ExampleSellerLogo.png" />
    <Content Include="Content\Images\exclamation.png" />
    <Content Include="Content\Images\fb_facebook.png" />
    <Content Include="Content\Images\Fields\Down.png" />
    <Content Include="Content\Images\Fields\Up.png" />
    <Content Include="Content\Images\fixedPriceBanner.png" />
    <Content Include="Content\Images\foot.png" />
    <Content Include="Content\Images\forgot_bg.jpg" />
    <Content Include="Content\Images\Full Name.png" />
    <Content Include="Content\Images\General\Camera.png" />
    <Content Include="Content\Images\General\clear.gif" />
    <Content Include="Content\Images\General\Delete.png" />
    <Content Include="Content\Images\General\error.png" />
    <Content Include="Content\Images\General\HelpTip.png" />
    <Content Include="Content\Images\General\jquery.ui.stars.gif" />
    <Content Include="Content\Images\General\no.png" />
    <Content Include="Content\Images\General\paidStamp.gif" />
    <Content Include="Content\Images\General\refresh-animated.gif" />
    <Content Include="Content\Images\General\rotate-clockwise.png" />
    <Content Include="Content\Images\General\scene-missing.jpg" />
    <Content Include="Content\Images\General\rotate-counterclockwise.png" />
    <Content Include="Content\Images\General\success.png" />
    <Content Include="Content\Images\General\warning.png" />
    <Content Include="Content\Images\General\yes.png" />
    <Content Include="Content\Images\ExampleListingThumb.jpg" />
    <Content Include="Content\Images\google_brand.png" />
    <Content Include="Content\Images\hotn.png" />
    <Content Include="Content\Images\Icons\pdf_icon_24x24.png" />
    <Content Include="Content\Images\Icon_Active.png" />
    <Content Include="Content\Images\Icon_Admin.png" />
    <Content Include="Content\Images\Icon_Buyer.png" />
    <Content Include="Content\Images\Icon_Consignor.png" />
    <Content Include="Content\Images\Icon_Deactivated.png" />
    <Content Include="Content\Images\Icon_Newsletter.png" />
    <Content Include="Content\Images\Icon_Restricted.png" />
    <Content Include="Content\Images\Icon_Seller.png" />
    <Content Include="Content\Images\Icon_Unapproved.png" />
    <Content Include="Content\Images\Icon_Unverified.png" />
    <Content Include="Content\Images\LightBox\close.png" />
    <Content Include="Content\Images\LightBox\loading.gif" />
    <Content Include="Content\Images\LightBox\next.png" />
    <Content Include="Content\Images\LightBox\prev.png" />
    <Content Include="Content\Images\login_bg.jpg" />
    <Content Include="Content\Images\Logos\AuctionWorxLogo31EN-240x40.png" />
    <Content Include="Content\Images\Logos\AuctionWorxLogo31EN-960x160.png" />
    <Content Include="Content\Images\Logos\AuctionWorxLogo31EV-240x40.png" />
    <Content Include="Content\Images\Logos\AuctionWorxLogo31EV-960x160.png" />
    <Content Include="Content\Images\Logos\Ent-360x60.png" />
    <Content Include="Content\Images\Logos\Ev-360x60.png" />
    <Content Include="Content\Images\mailbox.png" />
    <Content Include="Content\Images\nontax.png" />
    <Content Include="Content\Images\NoSearchResults.png" />
    <Content Include="Content\Images\notpaid1.png" />
    <Content Include="Content\Images\Paid.png" />
    <Content Include="Content\Images\paidStamp.png" />
    <Content Include="Content\Images\Password Field.png" />
    <Content Include="Content\Images\paypalbutton-1.gif" />
    <Content Include="Content\Images\placeholder-thumbnail.gif" />
    <Content Include="Content\Images\progress-wheel.gif" />
    <Content Include="Content\Images\rigester_bg.jpg" />
    <Content Include="Content\Images\righticons.png" />
    <Content Include="Content\Images\rightimges.png" />
    <Content Include="Content\Images\shippedStamp.png" />
    <Content Include="Content\Images\tax.png" />
    <Content Include="Content\font-awesome.css" />
    <Content Include="Content\Images\topheadcontact.png" />
    <Content Include="Content\Images\tw_twiter.png" />
    <Content Include="Content\Images\Username Icon.png" />
    <Content Include="Content\Images\Winter-2020-Collection-Menu-Tile-01_200x.jpg" />
    <Content Include="Content\Images\YouTube-icon-full_color.png" />
    <Content Include="Content\jquery-hex-colorpicker.css" />
    <Content Include="Content\themes\base\accordion.css" />
    <Content Include="Content\themes\base\all.css" />
    <Content Include="Content\themes\base\autocomplete.css" />
    <Content Include="Content\themes\base\base.css" />
    <Content Include="Content\themes\base\button.css" />
    <Content Include="Content\themes\base\core.css" />
    <Content Include="Content\themes\base\datepicker.css" />
    <Content Include="Content\themes\base\dialog.css" />
    <Content Include="Content\themes\base\draggable.css" />
    <Content Include="Content\themes\base\images\ui-bg_flat_0_aaaaaa_40x100.png" />
    <Content Include="Content\themes\base\images\ui-bg_flat_75_ffffff_40x100.png" />
    <Content Include="Content\themes\base\images\ui-bg_glass_55_fbf9ee_1x400.png" />
    <Content Include="Content\themes\base\images\ui-bg_glass_65_ffffff_1x400.png" />
    <Content Include="Content\themes\base\images\ui-bg_glass_75_dadada_1x400.png" />
    <Content Include="Content\themes\base\images\ui-bg_glass_75_e6e6e6_1x400.png" />
    <Content Include="Content\themes\base\images\ui-bg_glass_95_fef1ec_1x400.png" />
    <Content Include="Content\themes\base\images\ui-bg_highlight-soft_75_cccccc_1x100.png" />
    <Content Include="Content\themes\base\images\ui-icons_222222_256x240.png" />
    <Content Include="Content\themes\base\images\ui-icons_2e83ff_256x240.png" />
    <Content Include="Content\themes\base\images\ui-icons_444444_256x240.png" />
    <Content Include="Content\themes\base\images\ui-icons_454545_256x240.png" />
    <Content Include="Content\themes\base\images\ui-icons_555555_256x240.png" />
    <Content Include="Content\themes\base\images\ui-icons_777620_256x240.png" />
    <Content Include="Content\themes\base\images\ui-icons_777777_256x240.png" />
    <Content Include="Content\themes\base\images\ui-icons_888888_256x240.png" />
    <Content Include="Content\themes\base\images\ui-icons_cc0000_256x240.png" />
    <Content Include="Content\themes\base\images\ui-icons_cd0a0a_256x240.png" />
    <Content Include="Content\themes\base\images\ui-icons_ffffff_256x240.png" />
    <Content Include="Content\themes\base\jquery-ui.css" />
    <Content Include="Content\themes\base\jquery-ui.min.css" />
    <Content Include="Content\themes\base\menu.css" />
    <Content Include="Content\themes\base\progressbar.css" />
    <Content Include="Content\themes\base\resizable.css" />
    <Content Include="Content\themes\base\selectable.css" />
    <Content Include="Content\themes\base\selectmenu.css" />
    <Content Include="Content\themes\base\slider.css" />
    <Content Include="Content\themes\base\sortable.css" />
    <Content Include="Content\themes\base\spinner.css" />
    <Content Include="Content\themes\base\tabs.css" />
    <Content Include="Content\themes\base\theme.css" />
    <Content Include="Content\themes\base\tooltip.css" />
    <Content Include="fonts\fontawesome-webfont.svg" />
    <Content Include="Content\swf\swfupload.swf" />
    <Content Include="favicon.ico" />
    <Content Include="fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Global.asax" />
    <Content Include="IPN.aspx" />
    <Content Include="IpnTester.aspx" />
    <Content Include="PaymentResult.aspx" />
    <Content Include="Scripts\AWE-Dashboard.js" />
    <Content Include="Scripts\AWE-SignalR.js" />
    <Content Include="Scripts\bootstrap-dialog.js" />
    <Content Include="Scripts\bootstrap-dropdown.js" />
    <Content Include="Scripts\bootstrap-multiselect.js" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Scripts\ckeditor\adapters\jquery.js" />
    <Content Include="Scripts\ckeditor\build-config.js" />
    <Content Include="Scripts\ckeditor\ckeditor.js" />
    <Content Include="Scripts\ckeditor\config.js" />
    <Content Include="Scripts\ckeditor\contents.css" />
    <Content Include="Scripts\ckeditor\lang\af.js" />
    <Content Include="Scripts\ckeditor\lang\ar.js" />
    <Content Include="Scripts\ckeditor\lang\az.js" />
    <Content Include="Scripts\ckeditor\lang\bg.js" />
    <Content Include="Scripts\ckeditor\lang\bn.js" />
    <Content Include="Scripts\ckeditor\lang\bs.js" />
    <Content Include="Scripts\ckeditor\lang\ca.js" />
    <Content Include="Scripts\ckeditor\lang\cs.js" />
    <Content Include="Scripts\ckeditor\lang\cy.js" />
    <Content Include="Scripts\ckeditor\lang\da.js" />
    <Content Include="Scripts\ckeditor\lang\de-ch.js" />
    <Content Include="Scripts\ckeditor\lang\de.js" />
    <Content Include="Scripts\ckeditor\lang\el.js" />
    <Content Include="Scripts\ckeditor\lang\en-au.js" />
    <Content Include="Scripts\ckeditor\lang\en-ca.js" />
    <Content Include="Scripts\ckeditor\lang\en-gb.js" />
    <Content Include="Scripts\ckeditor\lang\en.js" />
    <Content Include="Scripts\ckeditor\lang\eo.js" />
    <Content Include="Scripts\ckeditor\lang\es-mx.js" />
    <Content Include="Scripts\ckeditor\lang\es.js" />
    <Content Include="Scripts\ckeditor\lang\et.js" />
    <Content Include="Scripts\ckeditor\lang\eu.js" />
    <Content Include="Scripts\ckeditor\lang\fa.js" />
    <Content Include="Scripts\ckeditor\lang\fi.js" />
    <Content Include="Scripts\ckeditor\lang\fo.js" />
    <Content Include="Scripts\ckeditor\lang\fr-ca.js" />
    <Content Include="Scripts\ckeditor\lang\fr.js" />
    <Content Include="Scripts\ckeditor\lang\gl.js" />
    <Content Include="Scripts\ckeditor\lang\gu.js" />
    <Content Include="Scripts\ckeditor\lang\he.js" />
    <Content Include="Scripts\ckeditor\lang\hi.js" />
    <Content Include="Scripts\ckeditor\lang\hr.js" />
    <Content Include="Scripts\ckeditor\lang\hu.js" />
    <Content Include="Scripts\ckeditor\lang\id.js" />
    <Content Include="Scripts\ckeditor\lang\is.js" />
    <Content Include="Scripts\ckeditor\lang\it.js" />
    <Content Include="Scripts\ckeditor\lang\ja.js" />
    <Content Include="Scripts\ckeditor\lang\ka.js" />
    <Content Include="Scripts\ckeditor\lang\km.js" />
    <Content Include="Scripts\ckeditor\lang\ko.js" />
    <Content Include="Scripts\ckeditor\lang\ku.js" />
    <Content Include="Scripts\ckeditor\lang\lt.js" />
    <Content Include="Scripts\ckeditor\lang\lv.js" />
    <Content Include="Scripts\ckeditor\lang\mk.js" />
    <Content Include="Scripts\ckeditor\lang\mn.js" />
    <Content Include="Scripts\ckeditor\lang\ms.js" />
    <Content Include="Scripts\ckeditor\lang\nb.js" />
    <Content Include="Scripts\ckeditor\lang\nl.js" />
    <Content Include="Scripts\ckeditor\lang\no.js" />
    <Content Include="Scripts\ckeditor\lang\oc.js" />
    <Content Include="Scripts\ckeditor\lang\pl.js" />
    <Content Include="Scripts\ckeditor\lang\pt-br.js" />
    <Content Include="Scripts\ckeditor\lang\pt.js" />
    <Content Include="Scripts\ckeditor\lang\ro.js" />
    <Content Include="Scripts\ckeditor\lang\ru.js" />
    <Content Include="Scripts\ckeditor\lang\si.js" />
    <Content Include="Scripts\ckeditor\lang\sk.js" />
    <Content Include="Scripts\ckeditor\lang\sl.js" />
    <Content Include="Scripts\ckeditor\lang\sq.js" />
    <Content Include="Scripts\ckeditor\lang\sr-latn.js" />
    <Content Include="Scripts\ckeditor\lang\sr.js" />
    <Content Include="Scripts\ckeditor\lang\sv.js" />
    <Content Include="Scripts\ckeditor\lang\th.js" />
    <Content Include="Scripts\ckeditor\lang\tr.js" />
    <Content Include="Scripts\ckeditor\lang\tt.js" />
    <Content Include="Scripts\ckeditor\lang\ug.js" />
    <Content Include="Scripts\ckeditor\lang\uk.js" />
    <Content Include="Scripts\ckeditor\lang\vi.js" />
    <Content Include="Scripts\ckeditor\lang\zh-cn.js" />
    <Content Include="Scripts\ckeditor\lang\zh.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\a11yhelp.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\af.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\ar.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\az.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\bg.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\ca.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\cs.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\cy.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\da.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\de-ch.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\de.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\el.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\en-au.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\en-gb.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\en.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\eo.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\es-mx.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\es.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\et.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\eu.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\fa.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\fi.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\fo.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\fr-ca.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\fr.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\gl.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\gu.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\he.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\hi.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\hr.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\hu.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\id.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\it.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\ja.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\km.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\ko.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\ku.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\lt.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\lv.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\mk.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\mn.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\nb.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\nl.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\no.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\oc.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\pl.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\pt-br.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\pt.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\ro.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\ru.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\si.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\sk.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\sl.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\sq.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\sr-latn.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\sr.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\sv.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\th.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\tr.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\tt.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\ug.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\uk.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\vi.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\zh-cn.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\zh.js" />
    <Content Include="Scripts\ckeditor\plugins\a11yhelp\dialogs\lang\_translationstatus.txt" />
    <Content Include="Scripts\ckeditor\plugins\about\dialogs\about.js" />
    <Content Include="Scripts\ckeditor\plugins\about\dialogs\hidpi\logo_ckeditor.png" />
    <Content Include="Scripts\ckeditor\plugins\about\dialogs\logo_ckeditor.png" />
    <Content Include="Scripts\ckeditor\plugins\colordialog\dialogs\colordialog.css" />
    <Content Include="Scripts\ckeditor\plugins\colordialog\dialogs\colordialog.js" />
    <Content Include="Scripts\ckeditor\plugins\copyformatting\cursors\cursor-disabled.svg" />
    <Content Include="Scripts\ckeditor\plugins\copyformatting\cursors\cursor.svg" />
    <Content Include="Scripts\ckeditor\plugins\copyformatting\styles\copyformatting.css" />
    <Content Include="Scripts\ckeditor\plugins\dialog\dialogDefinition.js" />
    <Content Include="Scripts\ckeditor\plugins\div\dialogs\div.js" />
    <Content Include="Scripts\ckeditor\plugins\find\dialogs\find.js" />
    <Content Include="Scripts\ckeditor\plugins\flash\dialogs\flash.js" />
    <Content Include="Scripts\ckeditor\plugins\flash\images\placeholder.png" />
    <Content Include="Scripts\ckeditor\plugins\forms\dialogs\button.js" />
    <Content Include="Scripts\ckeditor\plugins\forms\dialogs\checkbox.js" />
    <Content Include="Scripts\ckeditor\plugins\forms\dialogs\form.js" />
    <Content Include="Scripts\ckeditor\plugins\forms\dialogs\hiddenfield.js" />
    <Content Include="Scripts\ckeditor\plugins\forms\dialogs\radio.js" />
    <Content Include="Scripts\ckeditor\plugins\forms\dialogs\select.js" />
    <Content Include="Scripts\ckeditor\plugins\forms\dialogs\textarea.js" />
    <Content Include="Scripts\ckeditor\plugins\forms\dialogs\textfield.js" />
    <Content Include="Scripts\ckeditor\plugins\forms\images\hiddenfield.gif" />
    <Content Include="Scripts\ckeditor\plugins\icons.png" />
    <Content Include="Scripts\ckeditor\plugins\icons_hidpi.png" />
    <Content Include="Scripts\ckeditor\plugins\iframe\dialogs\iframe.js" />
    <Content Include="Scripts\ckeditor\plugins\iframe\images\placeholder.png" />
    <Content Include="Scripts\ckeditor\plugins\image\dialogs\image.js" />
    <Content Include="Scripts\ckeditor\plugins\image\images\noimage.png" />
    <Content Include="Scripts\ckeditor\plugins\link\dialogs\anchor.js" />
    <Content Include="Scripts\ckeditor\plugins\link\dialogs\link.js" />
    <Content Include="Scripts\ckeditor\plugins\link\images\anchor.png" />
    <Content Include="Scripts\ckeditor\plugins\link\images\hidpi\anchor.png" />
    <Content Include="Scripts\ckeditor\plugins\liststyle\dialogs\liststyle.js" />
    <Content Include="Scripts\ckeditor\plugins\magicline\images\hidpi\icon-rtl.png" />
    <Content Include="Scripts\ckeditor\plugins\magicline\images\hidpi\icon.png" />
    <Content Include="Scripts\ckeditor\plugins\magicline\images\icon-rtl.png" />
    <Content Include="Scripts\ckeditor\plugins\magicline\images\icon.png" />
    <Content Include="Scripts\ckeditor\plugins\pagebreak\images\pagebreak.gif" />
    <Content Include="Scripts\ckeditor\plugins\pastefromword\filter\default.js" />
    <Content Include="Scripts\ckeditor\plugins\preview\preview.html" />
    <Content Include="Scripts\ckeditor\plugins\scayt\dialogs\dialog.css" />
    <Content Include="Scripts\ckeditor\plugins\scayt\dialogs\options.js" />
    <Content Include="Scripts\ckeditor\plugins\scayt\dialogs\toolbar.css" />
    <Content Include="Scripts\ckeditor\plugins\scayt\skins\moono-lisa\scayt.css" />
    <Content Include="Scripts\ckeditor\plugins\showblocks\images\block_address.png" />
    <Content Include="Scripts\ckeditor\plugins\showblocks\images\block_blockquote.png" />
    <Content Include="Scripts\ckeditor\plugins\showblocks\images\block_div.png" />
    <Content Include="Scripts\ckeditor\plugins\showblocks\images\block_h1.png" />
    <Content Include="Scripts\ckeditor\plugins\showblocks\images\block_h2.png" />
    <Content Include="Scripts\ckeditor\plugins\showblocks\images\block_h3.png" />
    <Content Include="Scripts\ckeditor\plugins\showblocks\images\block_h4.png" />
    <Content Include="Scripts\ckeditor\plugins\showblocks\images\block_h5.png" />
    <Content Include="Scripts\ckeditor\plugins\showblocks\images\block_h6.png" />
    <Content Include="Scripts\ckeditor\plugins\showblocks\images\block_p.png" />
    <Content Include="Scripts\ckeditor\plugins\showblocks\images\block_pre.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\dialogs\smiley.js" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\angel_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\angel_smile.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\angry_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\angry_smile.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\broken_heart.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\broken_heart.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\confused_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\confused_smile.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\cry_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\cry_smile.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\devil_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\devil_smile.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\embaressed_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\embarrassed_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\embarrassed_smile.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\envelope.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\envelope.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\heart.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\heart.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\kiss.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\kiss.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\lightbulb.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\lightbulb.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\omg_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\omg_smile.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\regular_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\regular_smile.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\sad_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\sad_smile.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\shades_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\shades_smile.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\teeth_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\teeth_smile.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\thumbs_down.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\thumbs_down.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\thumbs_up.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\thumbs_up.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\tongue_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\tongue_smile.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\tounge_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\whatchutalkingabout_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\whatchutalkingabout_smile.png" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\wink_smile.gif" />
    <Content Include="Scripts\ckeditor\plugins\smiley\images\wink_smile.png" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\af.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\ar.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\az.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\bg.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\ca.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\cs.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\cy.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\da.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\de-ch.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\de.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\el.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\en-au.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\en-ca.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\en-gb.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\en.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\eo.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\es-mx.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\es.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\et.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\eu.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\fa.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\fi.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\fr-ca.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\fr.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\gl.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\he.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\hr.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\hu.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\id.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\it.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\ja.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\km.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\ko.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\ku.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\lt.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\lv.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\nb.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\nl.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\no.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\oc.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\pl.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\pt-br.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\pt.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\ro.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\ru.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\si.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\sk.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\sl.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\sq.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\sv.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\th.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\tr.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\tt.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\ug.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\uk.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\vi.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\zh-cn.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\zh.js" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\lang\_translationstatus.txt" />
    <Content Include="Scripts\ckeditor\plugins\specialchar\dialogs\specialchar.js" />
    <Content Include="Scripts\ckeditor\plugins\tableselection\styles\tableselection.css" />
    <Content Include="Scripts\ckeditor\plugins\tabletools\dialogs\tableCell.js" />
    <Content Include="Scripts\ckeditor\plugins\table\dialogs\table.js" />
    <Content Include="Scripts\ckeditor\plugins\templates\dialogs\templates.css" />
    <Content Include="Scripts\ckeditor\plugins\templates\dialogs\templates.js" />
    <Content Include="Scripts\ckeditor\plugins\templates\templates\default.js" />
    <Content Include="Scripts\ckeditor\plugins\templates\templates\images\template1.gif" />
    <Content Include="Scripts\ckeditor\plugins\templates\templates\images\template2.gif" />
    <Content Include="Scripts\ckeditor\plugins\templates\templates\images\template3.gif" />
    <Content Include="Scripts\ckeditor\plugins\widget\images\handle.png" />
    <Content Include="Scripts\ckeditor\plugins\wsc\dialogs\ciframe.html" />
    <Content Include="Scripts\ckeditor\plugins\wsc\dialogs\tmpFrameset.html" />
    <Content Include="Scripts\ckeditor\plugins\wsc\dialogs\wsc.css" />
    <Content Include="Scripts\ckeditor\plugins\wsc\dialogs\wsc.js" />
    <Content Include="Scripts\ckeditor\plugins\wsc\dialogs\wsc_ie.js" />
    <Content Include="Scripts\ckeditor\plugins\wsc\skins\moono-lisa\wsc.css" />
    <Content Include="Scripts\ckeditor\samples\css\samples.css" />
    <Content Include="Scripts\ckeditor\samples\img\github-top.png" />
    <Content Include="Scripts\ckeditor\samples\img\header-bg.png" />
    <Content Include="Scripts\ckeditor\samples\img\header-separator.png" />
    <Content Include="Scripts\ckeditor\samples\img\logo.png" />
    <Content Include="Scripts\ckeditor\samples\img\navigation-tip.png" />
    <Content Include="Scripts\ckeditor\samples\index.html" />
    <Content Include="Scripts\ckeditor\samples\js\sample.js" />
    <Content Include="Scripts\ckeditor\samples\js\sf.js" />
    <Content Include="Scripts\ckeditor\samples\old\ajax.html" />
    <Content Include="Scripts\ckeditor\samples\old\api.html" />
    <Content Include="Scripts\ckeditor\samples\old\appendto.html" />
    <Content Include="Scripts\ckeditor\samples\old\assets\inlineall\logo.png" />
    <Content Include="Scripts\ckeditor\samples\old\assets\outputxhtml\outputxhtml.css" />
    <Content Include="Scripts\ckeditor\samples\old\assets\posteddata.php" />
    <Content Include="Scripts\ckeditor\samples\old\assets\sample.jpg" />
    <Content Include="Scripts\ckeditor\samples\old\assets\uilanguages\languages.js" />
    <Content Include="Scripts\ckeditor\samples\old\datafiltering.html" />
    <Content Include="Scripts\ckeditor\samples\old\dialog\assets\my_dialog.js" />
    <Content Include="Scripts\ckeditor\samples\old\dialog\dialog.html" />
    <Content Include="Scripts\ckeditor\samples\old\divreplace.html" />
    <Content Include="Scripts\ckeditor\samples\old\enterkey\enterkey.html" />
    <Content Include="Scripts\ckeditor\samples\old\htmlwriter\assets\outputforflash\outputforflash.swf" />
    <Content Include="Scripts\ckeditor\samples\old\htmlwriter\assets\outputforflash\swfobject.js" />
    <Content Include="Scripts\ckeditor\samples\old\htmlwriter\outputforflash.html" />
    <Content Include="Scripts\ckeditor\samples\old\htmlwriter\outputhtml.html" />
    <Content Include="Scripts\ckeditor\samples\old\index.html" />
    <Content Include="Scripts\ckeditor\samples\old\inlineall.html" />
    <Content Include="Scripts\ckeditor\samples\old\inlinebycode.html" />
    <Content Include="Scripts\ckeditor\samples\old\inlinetextarea.html" />
    <Content Include="Scripts\ckeditor\samples\old\jquery.html" />
    <Content Include="Scripts\ckeditor\samples\old\magicline\magicline.html" />
    <Content Include="Scripts\ckeditor\samples\old\readonly.html" />
    <Content Include="Scripts\ckeditor\samples\old\replacebyclass.html" />
    <Content Include="Scripts\ckeditor\samples\old\replacebycode.html" />
    <Content Include="Scripts\ckeditor\samples\old\sample.css" />
    <Content Include="Scripts\ckeditor\samples\old\sample.js" />
    <Content Include="Scripts\ckeditor\samples\old\sample_posteddata.php" />
    <Content Include="Scripts\ckeditor\samples\old\tabindex.html" />
    <Content Include="Scripts\ckeditor\samples\old\toolbar\toolbar.html" />
    <Content Include="Scripts\ckeditor\samples\old\uicolor.html" />
    <Content Include="Scripts\ckeditor\samples\old\uilanguages.html" />
    <Content Include="Scripts\ckeditor\samples\old\wysiwygarea\fullpage.html" />
    <Content Include="Scripts\ckeditor\samples\old\xhtmlstyle.html" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\css\fontello.css" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\font\fontello.svg" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\font\LICENSE.txt" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\index.html" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\js\abstracttoolbarmodifier.js" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\js\fulltoolbareditor.js" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\js\toolbarmodifier.js" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\js\toolbartextmodifier.js" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\lib\codemirror\codemirror.css" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\lib\codemirror\codemirror.js" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\lib\codemirror\javascript.js" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\lib\codemirror\neo.css" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\lib\codemirror\show-hint.css" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\lib\codemirror\show-hint.js" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\dialog.css" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\dialog_ie.css" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\dialog_ie8.css" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\dialog_iequirks.css" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\editor.css" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\editor_gecko.css" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\editor_ie.css" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\editor_ie8.css" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\editor_iequirks.css" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\icons.png" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\icons_hidpi.png" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\images\arrow.png" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\images\close.png" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\images\hidpi\close.png" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\images\hidpi\lock-open.png" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\images\hidpi\lock.png" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\images\hidpi\refresh.png" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\images\lock-open.png" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\images\lock.png" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\images\refresh.png" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\images\spinner.gif" />
    <Content Include="Scripts\ckeditor\styles.js" />
    <Content Include="Scripts\core.js" />
    <Content Include="Scripts\FrameWorx.js" />
    <Content Include="Areas\API\Views\web.config" />
    <Content Include="Areas\API\Views\Test\Index.cshtml" />
    <Content Include="Areas\API\Views\Shared\Error.cshtml" />
    <Content Include="Areas\API\Views\Shared\_Layout.cshtml" />
    <Content Include="Areas\API\Views\TestCategory\Index.cshtml" />
    <Content Include="Areas\API\Views\TestCustomField\Index.cshtml" />
    <Content Include="Areas\API\Views\TestListingAction\Index.cshtml" />
    <Content Include="Areas\API\Views\TestListing\Index.cshtml" />
    <Content Include="Areas\API\Views\TestMedia\Index.cshtml" />
    <Content Include="Areas\API\Views\TestUseCase\Index.cshtml" />
    <Content Include="Areas\API\Views\_ViewStart.cshtml" />
    <Content Include="Areas\API\Views\TestSystem\Index.cshtml" />
    <Content Include="Areas\API\Views\TestUser\Index.cshtml" />
    <Content Include="fonts\glyphicons-halflings-regular.woff" />
    <Content Include="fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="fonts\glyphicons-halflings-regular.eot" />
    <Content Include="Areas\API\Views\TestSettings\Index.cshtml" />
    <Content Include="Scripts\globalize\cldr.min.js" />
    <Content Include="Scripts\globalize\json\supplemental\timeData.json" />
    <Content Include="Scripts\globalize\json\supplemental\weekData.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\en\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\en\characters.json" />
    <Content Include="Scripts\globalize\json\main\en\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\en\currencies.json" />
    <Content Include="Scripts\globalize\json\main\en\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\en\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\en\languages.json" />
    <Content Include="Scripts\globalize\json\main\en\layout.json" />
    <Content Include="Scripts\globalize\json\main\en\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\en\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\en\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\en\numbers.json" />
    <Content Include="Scripts\globalize\json\main\en\posix.json" />
    <Content Include="Scripts\globalize\json\main\en\scripts.json" />
    <Content Include="Scripts\globalize\json\main\en\territories.json" />
    <Content Include="Scripts\globalize\json\main\en\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\en\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\en\units.json" />
    <Content Include="Scripts\globalize\json\main\en\variants.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\fr\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\fr\characters.json" />
    <Content Include="Scripts\globalize\json\main\fr\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\fr\currencies.json" />
    <Content Include="Scripts\globalize\json\main\fr\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\fr\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\fr\languages.json" />
    <Content Include="Scripts\globalize\json\main\fr\layout.json" />
    <Content Include="Scripts\globalize\json\main\fr\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\fr\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\fr\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\fr\numbers.json" />
    <Content Include="Scripts\globalize\json\main\fr\posix.json" />
    <Content Include="Scripts\globalize\json\main\fr\scripts.json" />
    <Content Include="Scripts\globalize\json\main\fr\territories.json" />
    <Content Include="Scripts\globalize\json\main\fr\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\fr\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\fr\units.json" />
    <Content Include="Scripts\globalize\json\main\fr\variants.json" />
    <Content Include="Scripts\globalize\json\supplemental\likelySubtags.json" />
    <Content Include="Scripts\globalize\json\README" />
    <Content Include="Scripts\globalize\json\segments\de\exceptions.json" />
    <Content Include="Scripts\globalize\json\segments\el\exceptions.json" />
    <Content Include="Scripts\globalize\json\segments\en\exceptions.json" />
    <Content Include="Scripts\globalize\json\segments\es\exceptions.json" />
    <Content Include="Scripts\globalize\json\segments\fi\exceptions.json" />
    <Content Include="Scripts\globalize\json\segments\fr\exceptions.json" />
    <Content Include="Scripts\globalize\json\segments\it\exceptions.json" />
    <Content Include="Scripts\globalize\json\segments\ja\exceptions.json" />
    <Content Include="Scripts\globalize\json\segments\pt\exceptions.json" />
    <Content Include="Scripts\globalize\json\segments\root\exceptions.json" />
    <Content Include="Scripts\globalize\json\segments\ru\exceptions.json" />
    <Content Include="Scripts\globalize\json\supplemental\calendarData.json" />
    <Content Include="Scripts\globalize\json\supplemental\calendarPreferenceData.json" />
    <Content Include="Scripts\globalize\json\supplemental\characterFallbacks.json" />
    <Content Include="Scripts\globalize\json\supplemental\codeMappings.json" />
    <Content Include="Scripts\globalize\json\supplemental\currencyData.json" />
    <Content Include="Scripts\globalize\json\supplemental\dayPeriods.json" />
    <Content Include="Scripts\globalize\json\supplemental\gender.json" />
    <Content Include="Scripts\globalize\json\supplemental\languageData.json" />
    <Content Include="Scripts\globalize\json\supplemental\languageMatching.json" />
    <Content Include="Scripts\globalize\json\supplemental\measurementData.json" />
    <Content Include="Scripts\globalize\json\supplemental\metadata.json" />
    <Content Include="Scripts\globalize\json\supplemental\metaZones.json" />
    <Content Include="Scripts\globalize\json\supplemental\numberingSystems.json" />
    <Content Include="Scripts\globalize\json\supplemental\ordinals.json" />
    <Content Include="Scripts\globalize\json\supplemental\parentLocales.json" />
    <Content Include="Scripts\globalize\json\supplemental\plurals.json" />
    <Content Include="Scripts\globalize\json\supplemental\postalCodeData.json" />
    <Content Include="Scripts\globalize\json\supplemental\primaryZones.json" />
    <Content Include="Scripts\globalize\json\supplemental\references.json" />
    <Content Include="Scripts\globalize\json\supplemental\telephoneCodeData.json" />
    <Content Include="Scripts\globalize\json\supplemental\territoryContainment.json" />
    <Content Include="Scripts\globalize\json\supplemental\territoryInfo.json" />
    <Content Include="Scripts\globalize\json\supplemental\windowsZones.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\ar\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\ar\characters.json" />
    <Content Include="Scripts\globalize\json\main\ar\currencies.json" />
    <Content Include="Scripts\globalize\json\main\ar\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\ar\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\ar\languages.json" />
    <Content Include="Scripts\globalize\json\main\ar\layout.json" />
    <Content Include="Scripts\globalize\json\main\ar\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\ar\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\ar\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\ar\numbers.json" />
    <Content Include="Scripts\globalize\json\main\ar\posix.json" />
    <Content Include="Scripts\globalize\json\main\ar\scripts.json" />
    <Content Include="Scripts\globalize\json\main\ar\territories.json" />
    <Content Include="Scripts\globalize\json\main\ar\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\ar\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\ar\units.json" />
    <Content Include="Scripts\globalize\json\main\ar\variants.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\ca\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\ca\characters.json" />
    <Content Include="Scripts\globalize\json\main\ca\currencies.json" />
    <Content Include="Scripts\globalize\json\main\ca\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\ca\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\ca\languages.json" />
    <Content Include="Scripts\globalize\json\main\ca\layout.json" />
    <Content Include="Scripts\globalize\json\main\ca\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\ca\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\ca\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\ca\numbers.json" />
    <Content Include="Scripts\globalize\json\main\ca\posix.json" />
    <Content Include="Scripts\globalize\json\main\ca\scripts.json" />
    <Content Include="Scripts\globalize\json\main\ca\territories.json" />
    <Content Include="Scripts\globalize\json\main\ca\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\ca\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\ca\units.json" />
    <Content Include="Scripts\globalize\json\main\ca\variants.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\cs\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\cs\characters.json" />
    <Content Include="Scripts\globalize\json\main\cs\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\cs\currencies.json" />
    <Content Include="Scripts\globalize\json\main\cs\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\cs\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\cs\languages.json" />
    <Content Include="Scripts\globalize\json\main\cs\layout.json" />
    <Content Include="Scripts\globalize\json\main\cs\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\cs\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\cs\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\cs\numbers.json" />
    <Content Include="Scripts\globalize\json\main\cs\posix.json" />
    <Content Include="Scripts\globalize\json\main\cs\scripts.json" />
    <Content Include="Scripts\globalize\json\main\cs\territories.json" />
    <Content Include="Scripts\globalize\json\main\cs\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\cs\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\cs\units.json" />
    <Content Include="Scripts\globalize\json\main\cs\variants.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\da\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\da\characters.json" />
    <Content Include="Scripts\globalize\json\main\da\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\da\currencies.json" />
    <Content Include="Scripts\globalize\json\main\da\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\da\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\da\languages.json" />
    <Content Include="Scripts\globalize\json\main\da\layout.json" />
    <Content Include="Scripts\globalize\json\main\da\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\da\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\da\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\da\numbers.json" />
    <Content Include="Scripts\globalize\json\main\da\posix.json" />
    <Content Include="Scripts\globalize\json\main\da\scripts.json" />
    <Content Include="Scripts\globalize\json\main\da\territories.json" />
    <Content Include="Scripts\globalize\json\main\da\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\da\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\da\units.json" />
    <Content Include="Scripts\globalize\json\main\da\variants.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\de\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\de\characters.json" />
    <Content Include="Scripts\globalize\json\main\de\currencies.json" />
    <Content Include="Scripts\globalize\json\main\de\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\de\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\de\languages.json" />
    <Content Include="Scripts\globalize\json\main\de\layout.json" />
    <Content Include="Scripts\globalize\json\main\de\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\de\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\de\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\de\numbers.json" />
    <Content Include="Scripts\globalize\json\main\de\posix.json" />
    <Content Include="Scripts\globalize\json\main\de\scripts.json" />
    <Content Include="Scripts\globalize\json\main\de\territories.json" />
    <Content Include="Scripts\globalize\json\main\de\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\de\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\de\units.json" />
    <Content Include="Scripts\globalize\json\main\de\variants.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\el\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\el\characters.json" />
    <Content Include="Scripts\globalize\json\main\el\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\el\currencies.json" />
    <Content Include="Scripts\globalize\json\main\el\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\el\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\el\languages.json" />
    <Content Include="Scripts\globalize\json\main\el\layout.json" />
    <Content Include="Scripts\globalize\json\main\el\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\el\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\el\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\el\numbers.json" />
    <Content Include="Scripts\globalize\json\main\el\posix.json" />
    <Content Include="Scripts\globalize\json\main\el\scripts.json" />
    <Content Include="Scripts\globalize\json\main\el\territories.json" />
    <Content Include="Scripts\globalize\json\main\el\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\el\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\el\units.json" />
    <Content Include="Scripts\globalize\json\main\el\variants.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\en-001\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\en-001\characters.json" />
    <Content Include="Scripts\globalize\json\main\en-001\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\en-001\currencies.json" />
    <Content Include="Scripts\globalize\json\main\en-001\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\en-001\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\en-001\languages.json" />
    <Content Include="Scripts\globalize\json\main\en-001\layout.json" />
    <Content Include="Scripts\globalize\json\main\en-001\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\en-001\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\en-001\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\en-001\numbers.json" />
    <Content Include="Scripts\globalize\json\main\en-001\posix.json" />
    <Content Include="Scripts\globalize\json\main\en-001\scripts.json" />
    <Content Include="Scripts\globalize\json\main\en-001\territories.json" />
    <Content Include="Scripts\globalize\json\main\en-001\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\en-001\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\en-001\units.json" />
    <Content Include="Scripts\globalize\json\main\en-001\variants.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\characters.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\currencies.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\languages.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\layout.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\numbers.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\posix.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\scripts.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\territories.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\units.json" />
    <Content Include="Scripts\globalize\json\main\en-AU\variants.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\characters.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\currencies.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\languages.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\layout.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\numbers.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\posix.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\scripts.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\territories.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\units.json" />
    <Content Include="Scripts\globalize\json\main\en-CA\variants.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\characters.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\currencies.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\languages.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\layout.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\numbers.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\posix.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\scripts.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\territories.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\units.json" />
    <Content Include="Scripts\globalize\json\main\en-GB\variants.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\characters.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\currencies.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\languages.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\layout.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\numbers.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\posix.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\scripts.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\territories.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\units.json" />
    <Content Include="Scripts\globalize\json\main\en-HK\variants.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\characters.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\currencies.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\languages.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\layout.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\numbers.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\posix.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\scripts.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\territories.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\units.json" />
    <Content Include="Scripts\globalize\json\main\en-IN\variants.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\es\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\es\characters.json" />
    <Content Include="Scripts\globalize\json\main\es\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\es\currencies.json" />
    <Content Include="Scripts\globalize\json\main\es\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\es\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\es\languages.json" />
    <Content Include="Scripts\globalize\json\main\es\layout.json" />
    <Content Include="Scripts\globalize\json\main\es\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\es\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\es\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\es\numbers.json" />
    <Content Include="Scripts\globalize\json\main\es\posix.json" />
    <Content Include="Scripts\globalize\json\main\es\scripts.json" />
    <Content Include="Scripts\globalize\json\main\es\territories.json" />
    <Content Include="Scripts\globalize\json\main\es\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\es\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\es\units.json" />
    <Content Include="Scripts\globalize\json\main\es\variants.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\fi\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\fi\characters.json" />
    <Content Include="Scripts\globalize\json\main\fi\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\fi\currencies.json" />
    <Content Include="Scripts\globalize\json\main\fi\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\fi\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\fi\languages.json" />
    <Content Include="Scripts\globalize\json\main\fi\layout.json" />
    <Content Include="Scripts\globalize\json\main\fi\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\fi\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\fi\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\fi\numbers.json" />
    <Content Include="Scripts\globalize\json\main\fi\posix.json" />
    <Content Include="Scripts\globalize\json\main\fi\scripts.json" />
    <Content Include="Scripts\globalize\json\main\fi\territories.json" />
    <Content Include="Scripts\globalize\json\main\fi\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\fi\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\fi\units.json" />
    <Content Include="Scripts\globalize\json\main\fi\variants.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\he\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\he\characters.json" />
    <Content Include="Scripts\globalize\json\main\he\currencies.json" />
    <Content Include="Scripts\globalize\json\main\he\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\he\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\he\languages.json" />
    <Content Include="Scripts\globalize\json\main\he\layout.json" />
    <Content Include="Scripts\globalize\json\main\he\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\he\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\he\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\he\numbers.json" />
    <Content Include="Scripts\globalize\json\main\he\posix.json" />
    <Content Include="Scripts\globalize\json\main\he\scripts.json" />
    <Content Include="Scripts\globalize\json\main\he\territories.json" />
    <Content Include="Scripts\globalize\json\main\he\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\he\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\he\units.json" />
    <Content Include="Scripts\globalize\json\main\he\variants.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\hi\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\hi\characters.json" />
    <Content Include="Scripts\globalize\json\main\hi\currencies.json" />
    <Content Include="Scripts\globalize\json\main\hi\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\hi\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\hi\languages.json" />
    <Content Include="Scripts\globalize\json\main\hi\layout.json" />
    <Content Include="Scripts\globalize\json\main\hi\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\hi\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\hi\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\hi\numbers.json" />
    <Content Include="Scripts\globalize\json\main\hi\posix.json" />
    <Content Include="Scripts\globalize\json\main\hi\scripts.json" />
    <Content Include="Scripts\globalize\json\main\hi\territories.json" />
    <Content Include="Scripts\globalize\json\main\hi\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\hi\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\hi\units.json" />
    <Content Include="Scripts\globalize\json\main\hi\variants.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\hr\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\hr\characters.json" />
    <Content Include="Scripts\globalize\json\main\hr\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\hr\currencies.json" />
    <Content Include="Scripts\globalize\json\main\hr\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\hr\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\hr\languages.json" />
    <Content Include="Scripts\globalize\json\main\hr\layout.json" />
    <Content Include="Scripts\globalize\json\main\hr\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\hr\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\hr\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\hr\numbers.json" />
    <Content Include="Scripts\globalize\json\main\hr\posix.json" />
    <Content Include="Scripts\globalize\json\main\hr\scripts.json" />
    <Content Include="Scripts\globalize\json\main\hr\territories.json" />
    <Content Include="Scripts\globalize\json\main\hr\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\hr\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\hr\units.json" />
    <Content Include="Scripts\globalize\json\main\hr\variants.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\hu\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\hu\characters.json" />
    <Content Include="Scripts\globalize\json\main\hu\currencies.json" />
    <Content Include="Scripts\globalize\json\main\hu\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\hu\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\hu\languages.json" />
    <Content Include="Scripts\globalize\json\main\hu\layout.json" />
    <Content Include="Scripts\globalize\json\main\hu\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\hu\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\hu\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\hu\numbers.json" />
    <Content Include="Scripts\globalize\json\main\hu\posix.json" />
    <Content Include="Scripts\globalize\json\main\hu\scripts.json" />
    <Content Include="Scripts\globalize\json\main\hu\territories.json" />
    <Content Include="Scripts\globalize\json\main\hu\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\hu\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\hu\units.json" />
    <Content Include="Scripts\globalize\json\main\hu\variants.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\it\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\it\characters.json" />
    <Content Include="Scripts\globalize\json\main\it\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\it\currencies.json" />
    <Content Include="Scripts\globalize\json\main\it\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\it\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\it\languages.json" />
    <Content Include="Scripts\globalize\json\main\it\layout.json" />
    <Content Include="Scripts\globalize\json\main\it\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\it\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\it\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\it\numbers.json" />
    <Content Include="Scripts\globalize\json\main\it\posix.json" />
    <Content Include="Scripts\globalize\json\main\it\scripts.json" />
    <Content Include="Scripts\globalize\json\main\it\territories.json" />
    <Content Include="Scripts\globalize\json\main\it\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\it\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\it\units.json" />
    <Content Include="Scripts\globalize\json\main\it\variants.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\ja\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\ja\characters.json" />
    <Content Include="Scripts\globalize\json\main\ja\currencies.json" />
    <Content Include="Scripts\globalize\json\main\ja\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\ja\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\ja\languages.json" />
    <Content Include="Scripts\globalize\json\main\ja\layout.json" />
    <Content Include="Scripts\globalize\json\main\ja\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\ja\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\ja\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\ja\numbers.json" />
    <Content Include="Scripts\globalize\json\main\ja\posix.json" />
    <Content Include="Scripts\globalize\json\main\ja\scripts.json" />
    <Content Include="Scripts\globalize\json\main\ja\territories.json" />
    <Content Include="Scripts\globalize\json\main\ja\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\ja\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\ja\units.json" />
    <Content Include="Scripts\globalize\json\main\ja\variants.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\ko\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\ko\characters.json" />
    <Content Include="Scripts\globalize\json\main\ko\currencies.json" />
    <Content Include="Scripts\globalize\json\main\ko\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\ko\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\ko\languages.json" />
    <Content Include="Scripts\globalize\json\main\ko\layout.json" />
    <Content Include="Scripts\globalize\json\main\ko\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\ko\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\ko\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\ko\numbers.json" />
    <Content Include="Scripts\globalize\json\main\ko\posix.json" />
    <Content Include="Scripts\globalize\json\main\ko\scripts.json" />
    <Content Include="Scripts\globalize\json\main\ko\territories.json" />
    <Content Include="Scripts\globalize\json\main\ko\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\ko\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\ko\units.json" />
    <Content Include="Scripts\globalize\json\main\ko\variants.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\nb\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\nb\characters.json" />
    <Content Include="Scripts\globalize\json\main\nb\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\nb\currencies.json" />
    <Content Include="Scripts\globalize\json\main\nb\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\nb\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\nb\languages.json" />
    <Content Include="Scripts\globalize\json\main\nb\layout.json" />
    <Content Include="Scripts\globalize\json\main\nb\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\nb\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\nb\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\nb\numbers.json" />
    <Content Include="Scripts\globalize\json\main\nb\posix.json" />
    <Content Include="Scripts\globalize\json\main\nb\scripts.json" />
    <Content Include="Scripts\globalize\json\main\nb\territories.json" />
    <Content Include="Scripts\globalize\json\main\nb\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\nb\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\nb\units.json" />
    <Content Include="Scripts\globalize\json\main\nb\variants.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\nl\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\nl\characters.json" />
    <Content Include="Scripts\globalize\json\main\nl\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\nl\currencies.json" />
    <Content Include="Scripts\globalize\json\main\nl\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\nl\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\nl\languages.json" />
    <Content Include="Scripts\globalize\json\main\nl\layout.json" />
    <Content Include="Scripts\globalize\json\main\nl\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\nl\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\nl\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\nl\numbers.json" />
    <Content Include="Scripts\globalize\json\main\nl\posix.json" />
    <Content Include="Scripts\globalize\json\main\nl\scripts.json" />
    <Content Include="Scripts\globalize\json\main\nl\territories.json" />
    <Content Include="Scripts\globalize\json\main\nl\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\nl\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\nl\units.json" />
    <Content Include="Scripts\globalize\json\main\nl\variants.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\pl\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\pl\characters.json" />
    <Content Include="Scripts\globalize\json\main\pl\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\pl\currencies.json" />
    <Content Include="Scripts\globalize\json\main\pl\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\pl\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\pl\languages.json" />
    <Content Include="Scripts\globalize\json\main\pl\layout.json" />
    <Content Include="Scripts\globalize\json\main\pl\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\pl\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\pl\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\pl\numbers.json" />
    <Content Include="Scripts\globalize\json\main\pl\posix.json" />
    <Content Include="Scripts\globalize\json\main\pl\scripts.json" />
    <Content Include="Scripts\globalize\json\main\pl\territories.json" />
    <Content Include="Scripts\globalize\json\main\pl\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\pl\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\pl\units.json" />
    <Content Include="Scripts\globalize\json\main\pl\variants.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\characters.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\currencies.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\languages.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\layout.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\numbers.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\posix.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\scripts.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\territories.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\units.json" />
    <Content Include="Scripts\globalize\json\main\pt-PT\variants.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\pt\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\pt\characters.json" />
    <Content Include="Scripts\globalize\json\main\pt\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\pt\currencies.json" />
    <Content Include="Scripts\globalize\json\main\pt\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\pt\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\pt\languages.json" />
    <Content Include="Scripts\globalize\json\main\pt\layout.json" />
    <Content Include="Scripts\globalize\json\main\pt\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\pt\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\pt\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\pt\numbers.json" />
    <Content Include="Scripts\globalize\json\main\pt\posix.json" />
    <Content Include="Scripts\globalize\json\main\pt\scripts.json" />
    <Content Include="Scripts\globalize\json\main\pt\territories.json" />
    <Content Include="Scripts\globalize\json\main\pt\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\pt\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\pt\units.json" />
    <Content Include="Scripts\globalize\json\main\pt\variants.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\root\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\root\characters.json" />
    <Content Include="Scripts\globalize\json\main\root\currencies.json" />
    <Content Include="Scripts\globalize\json\main\root\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\root\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\root\languages.json" />
    <Content Include="Scripts\globalize\json\main\root\layout.json" />
    <Content Include="Scripts\globalize\json\main\root\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\root\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\root\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\root\numbers.json" />
    <Content Include="Scripts\globalize\json\main\root\posix.json" />
    <Content Include="Scripts\globalize\json\main\root\scripts.json" />
    <Content Include="Scripts\globalize\json\main\root\territories.json" />
    <Content Include="Scripts\globalize\json\main\root\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\root\units.json" />
    <Content Include="Scripts\globalize\json\main\root\variants.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\ro\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\ro\characters.json" />
    <Content Include="Scripts\globalize\json\main\ro\currencies.json" />
    <Content Include="Scripts\globalize\json\main\ro\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\ro\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\ro\languages.json" />
    <Content Include="Scripts\globalize\json\main\ro\layout.json" />
    <Content Include="Scripts\globalize\json\main\ro\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\ro\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\ro\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\ro\numbers.json" />
    <Content Include="Scripts\globalize\json\main\ro\posix.json" />
    <Content Include="Scripts\globalize\json\main\ro\scripts.json" />
    <Content Include="Scripts\globalize\json\main\ro\territories.json" />
    <Content Include="Scripts\globalize\json\main\ro\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\ro\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\ro\units.json" />
    <Content Include="Scripts\globalize\json\main\ro\variants.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\ru\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\ru\characters.json" />
    <Content Include="Scripts\globalize\json\main\ru\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\ru\currencies.json" />
    <Content Include="Scripts\globalize\json\main\ru\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\ru\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\ru\languages.json" />
    <Content Include="Scripts\globalize\json\main\ru\layout.json" />
    <Content Include="Scripts\globalize\json\main\ru\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\ru\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\ru\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\ru\numbers.json" />
    <Content Include="Scripts\globalize\json\main\ru\posix.json" />
    <Content Include="Scripts\globalize\json\main\ru\scripts.json" />
    <Content Include="Scripts\globalize\json\main\ru\territories.json" />
    <Content Include="Scripts\globalize\json\main\ru\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\ru\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\ru\units.json" />
    <Content Include="Scripts\globalize\json\main\ru\variants.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\sk\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\sk\characters.json" />
    <Content Include="Scripts\globalize\json\main\sk\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\sk\currencies.json" />
    <Content Include="Scripts\globalize\json\main\sk\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\sk\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\sk\languages.json" />
    <Content Include="Scripts\globalize\json\main\sk\layout.json" />
    <Content Include="Scripts\globalize\json\main\sk\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\sk\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\sk\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\sk\numbers.json" />
    <Content Include="Scripts\globalize\json\main\sk\posix.json" />
    <Content Include="Scripts\globalize\json\main\sk\scripts.json" />
    <Content Include="Scripts\globalize\json\main\sk\territories.json" />
    <Content Include="Scripts\globalize\json\main\sk\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\sk\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\sk\units.json" />
    <Content Include="Scripts\globalize\json\main\sk\variants.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\sl\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\sl\characters.json" />
    <Content Include="Scripts\globalize\json\main\sl\currencies.json" />
    <Content Include="Scripts\globalize\json\main\sl\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\sl\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\sl\languages.json" />
    <Content Include="Scripts\globalize\json\main\sl\layout.json" />
    <Content Include="Scripts\globalize\json\main\sl\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\sl\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\sl\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\sl\numbers.json" />
    <Content Include="Scripts\globalize\json\main\sl\posix.json" />
    <Content Include="Scripts\globalize\json\main\sl\scripts.json" />
    <Content Include="Scripts\globalize\json\main\sl\territories.json" />
    <Content Include="Scripts\globalize\json\main\sl\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\sl\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\sl\units.json" />
    <Content Include="Scripts\globalize\json\main\sl\variants.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\sr\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\sr\characters.json" />
    <Content Include="Scripts\globalize\json\main\sr\currencies.json" />
    <Content Include="Scripts\globalize\json\main\sr\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\sr\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\sr\languages.json" />
    <Content Include="Scripts\globalize\json\main\sr\layout.json" />
    <Content Include="Scripts\globalize\json\main\sr\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\sr\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\sr\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\sr\numbers.json" />
    <Content Include="Scripts\globalize\json\main\sr\posix.json" />
    <Content Include="Scripts\globalize\json\main\sr\scripts.json" />
    <Content Include="Scripts\globalize\json\main\sr\territories.json" />
    <Content Include="Scripts\globalize\json\main\sr\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\sr\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\sr\units.json" />
    <Content Include="Scripts\globalize\json\main\sr\variants.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\sv\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\sv\characters.json" />
    <Content Include="Scripts\globalize\json\main\sv\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\sv\currencies.json" />
    <Content Include="Scripts\globalize\json\main\sv\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\sv\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\sv\languages.json" />
    <Content Include="Scripts\globalize\json\main\sv\layout.json" />
    <Content Include="Scripts\globalize\json\main\sv\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\sv\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\sv\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\sv\numbers.json" />
    <Content Include="Scripts\globalize\json\main\sv\posix.json" />
    <Content Include="Scripts\globalize\json\main\sv\scripts.json" />
    <Content Include="Scripts\globalize\json\main\sv\territories.json" />
    <Content Include="Scripts\globalize\json\main\sv\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\sv\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\sv\units.json" />
    <Content Include="Scripts\globalize\json\main\sv\variants.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\th\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\th\characters.json" />
    <Content Include="Scripts\globalize\json\main\th\currencies.json" />
    <Content Include="Scripts\globalize\json\main\th\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\th\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\th\languages.json" />
    <Content Include="Scripts\globalize\json\main\th\layout.json" />
    <Content Include="Scripts\globalize\json\main\th\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\th\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\th\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\th\numbers.json" />
    <Content Include="Scripts\globalize\json\main\th\posix.json" />
    <Content Include="Scripts\globalize\json\main\th\scripts.json" />
    <Content Include="Scripts\globalize\json\main\th\territories.json" />
    <Content Include="Scripts\globalize\json\main\th\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\th\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\th\units.json" />
    <Content Include="Scripts\globalize\json\main\th\variants.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\tr\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\tr\characters.json" />
    <Content Include="Scripts\globalize\json\main\tr\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\tr\currencies.json" />
    <Content Include="Scripts\globalize\json\main\tr\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\tr\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\tr\languages.json" />
    <Content Include="Scripts\globalize\json\main\tr\layout.json" />
    <Content Include="Scripts\globalize\json\main\tr\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\tr\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\tr\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\tr\numbers.json" />
    <Content Include="Scripts\globalize\json\main\tr\posix.json" />
    <Content Include="Scripts\globalize\json\main\tr\scripts.json" />
    <Content Include="Scripts\globalize\json\main\tr\territories.json" />
    <Content Include="Scripts\globalize\json\main\tr\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\tr\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\tr\units.json" />
    <Content Include="Scripts\globalize\json\main\tr\variants.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\uk\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\uk\characters.json" />
    <Content Include="Scripts\globalize\json\main\uk\contextTransforms.json" />
    <Content Include="Scripts\globalize\json\main\uk\currencies.json" />
    <Content Include="Scripts\globalize\json\main\uk\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\uk\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\uk\languages.json" />
    <Content Include="Scripts\globalize\json\main\uk\layout.json" />
    <Content Include="Scripts\globalize\json\main\uk\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\uk\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\uk\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\uk\numbers.json" />
    <Content Include="Scripts\globalize\json\main\uk\posix.json" />
    <Content Include="Scripts\globalize\json\main\uk\scripts.json" />
    <Content Include="Scripts\globalize\json\main\uk\territories.json" />
    <Content Include="Scripts\globalize\json\main\uk\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\uk\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\uk\units.json" />
    <Content Include="Scripts\globalize\json\main\uk\variants.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\vi\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\vi\characters.json" />
    <Content Include="Scripts\globalize\json\main\vi\currencies.json" />
    <Content Include="Scripts\globalize\json\main\vi\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\vi\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\vi\languages.json" />
    <Content Include="Scripts\globalize\json\main\vi\layout.json" />
    <Content Include="Scripts\globalize\json\main\vi\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\vi\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\vi\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\vi\numbers.json" />
    <Content Include="Scripts\globalize\json\main\vi\posix.json" />
    <Content Include="Scripts\globalize\json\main\vi\scripts.json" />
    <Content Include="Scripts\globalize\json\main\vi\territories.json" />
    <Content Include="Scripts\globalize\json\main\vi\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\vi\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\vi\units.json" />
    <Content Include="Scripts\globalize\json\main\vi\variants.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\characters.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\currencies.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\languages.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\layout.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\numbers.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\posix.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\scripts.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\territories.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\units.json" />
    <Content Include="Scripts\globalize\json\main\zh-Hant\variants.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-buddhist.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-chinese.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-coptic.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-dangi.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-ethiopic-amete-alem.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-ethiopic.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-generic.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-gregorian.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-hebrew.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-indian.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-islamic-civil.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-islamic-rgsa.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-islamic-tbla.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-islamic-umalqura.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-islamic.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-japanese.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-persian.json" />
    <Content Include="Scripts\globalize\json\main\zh\ca-roc.json" />
    <Content Include="Scripts\globalize\json\main\zh\characters.json" />
    <Content Include="Scripts\globalize\json\main\zh\currencies.json" />
    <Content Include="Scripts\globalize\json\main\zh\dateFields.json" />
    <Content Include="Scripts\globalize\json\main\zh\delimiters.json" />
    <Content Include="Scripts\globalize\json\main\zh\languages.json" />
    <Content Include="Scripts\globalize\json\main\zh\layout.json" />
    <Content Include="Scripts\globalize\json\main\zh\listPatterns.json" />
    <Content Include="Scripts\globalize\json\main\zh\localeDisplayNames.json" />
    <Content Include="Scripts\globalize\json\main\zh\measurementSystemNames.json" />
    <Content Include="Scripts\globalize\json\main\zh\numbers.json" />
    <Content Include="Scripts\globalize\json\main\zh\posix.json" />
    <Content Include="Scripts\globalize\json\main\zh\scripts.json" />
    <Content Include="Scripts\globalize\json\main\zh\territories.json" />
    <Content Include="Scripts\globalize\json\main\zh\timeZoneNames.json" />
    <Content Include="Scripts\globalize\json\main\zh\transformNames.json" />
    <Content Include="Scripts\globalize\json\main\zh\units.json" />
    <Content Include="Scripts\globalize\json\main\zh\variants.json" />
    <Content Include="fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="fonts\fontawesome-webfont.eot" />
    <Content Include="fonts\fontawesome-webfont.ttf" />
    <Content Include="fonts\fontawesome-webfont.woff" />
    <Content Include="fonts\fontawesome-webfont.woff2" />
    <Content Include="fonts\FontAwesome.otf" />
    <Content Include="Areas\HelpPage\Views\_ViewStart.cshtml" />
    <Content Include="Areas\HelpPage\Views\Web.config" />
    <Content Include="Areas\HelpPage\Views\Shared\_Layout.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\Index.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\TextSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\Samples.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\Parameters.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\InvalidSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ImageSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\HelpPageApiModel.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ApiGroup.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\Api.cshtml" />
    <Content Include="Scripts\globalize\cldr.js" />
    <Content Include="Scripts\globalize\cldr\event.js" />
    <Content Include="Scripts\globalize\cldr\event.min.js" />
    <Content Include="Scripts\globalize\cldr\supplemental.js" />
    <Content Include="Scripts\globalize\cldr\supplemental.min.js" />
    <Content Include="Scripts\globalize\cldr\unresolved.js" />
    <Content Include="Scripts\globalize\cldr\unresolved.min.js" />
    <Content Include="Scripts\globalize\globalize.js" />
    <Content Include="Scripts\globalize\globalize.min.js" />
    <Content Include="Scripts\globalize\globalize\date.js" />
    <Content Include="Scripts\globalize\globalize\date.min.js" />
    <Content Include="Scripts\globalize\globalize\message.js" />
    <Content Include="Scripts\globalize\globalize\message.min.js" />
    <Content Include="Scripts\globalize\globalize\number.js" />
    <Content Include="Scripts\globalize\globalize\number.min.js" />
    <Content Include="Scripts\globalize\json\unicode-license.txt" />
    <Content Include="Scripts\html5shiv.min.js" />
    <Content Include="Scripts\ckeditor\CHANGES.md" />
    <Content Include="Scripts\ckeditor\LICENSE.md" />
    <Content Include="Scripts\ckeditor\plugins\scayt\CHANGELOG.md" />
    <Content Include="Scripts\ckeditor\plugins\scayt\LICENSE.md" />
    <Content Include="Scripts\ckeditor\plugins\scayt\README.md" />
    <Content Include="Scripts\ckeditor\plugins\wsc\LICENSE.md" />
    <Content Include="Scripts\ckeditor\plugins\wsc\README.md" />
    <Content Include="Scripts\ckeditor\README.md" />
    <Content Include="Scripts\ckeditor\samples\old\htmlwriter\assets\outputforflash\outputforflash.fla" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\font\config.json" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\font\fontello.eot" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\font\fontello.ttf" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\font\fontello.woff" />
    <Content Include="Scripts\ckeditor\samples\toolbarconfigurator\lib\codemirror\LICENSE" />
    <Content Include="Scripts\ckeditor\skins\moono-lisa\readme.md" />
    <Content Include="Content\font\1786-webfont.woff" />
    <Content Include="Content\font\1786-webfont.woff2" />
    <Content Include="Content\font\azaelia-webfont.woff" />
    <Content Include="Content\font\azaelia-webfont.woff2" />
    <Content Include="Content\font\baloney_-webfont.woff" />
    <Content Include="Content\font\baloney_-webfont.woff2" />
    <Content Include="Content\font\cooperhewitt-book-webfont.woff" />
    <Content Include="Content\font\cooperhewitt-book-webfont.woff2" />
    <Content Include="Content\font\floyd-webfont.woff" />
    <Content Include="Content\font\floyd-webfont.woff2" />
    <Content Include="Content\font\natural-webfont.woff" />
    <Content Include="Content\font\natural-webfont.woff2" />
    <Content Include="Content\font\new_againts-webfont.woff" />
    <Content Include="Content\font\new_againts-webfont.woff2" />
    <Content Include="Content\font\okaycat_-_lolo_cursive-webfont.woff" />
    <Content Include="Content\font\okaycat_-_lolo_cursive-webfont.woff2" />
    <Content Include="Content\font\ont-webfont.woff" />
    <Content Include="Content\font\ont-webfont.woff2" />
    <Content Include="Content\font\solferino-webfont.woff" />
    <Content Include="Content\font\solferino-webfont.woff2" />
    <Content Include="Content\Images\banner.webp" />
    <Content Include="Content\Images\1.webp" />
    <Content Include="Content\Images\2.webp" />
    <Content Include="Content\Images\3.webp" />
    <Content Include="Content\Images\4.webp" />
    <Content Include="Content\Images\5.webp" />
    <Content Include="Content\Images\7.webp" />
    <Content Include="Content\Images\8.webp" />
    <Content Include="Content\Images\9.webp" />
    <Content Include="Content\Images\ss_1.webp" />
    <Content Include="Content\Images\ss_10.webp" />
    <Content Include="Content\Images\ss_2.webp" />
    <Content Include="Content\Images\ss_3.webp" />
    <Content Include="Content\Images\ss_4.webp" />
    <Content Include="Content\Images\ss_5.webp" />
    <Content Include="Content\Images\ss_6.webp" />
    <Content Include="Content\Images\ss_7.webp" />
    <Content Include="Content\Images\ss_8.webp" />
    <Content Include="Content\Images\ss_9.webp" />
    <Content Include="content-api-key.json" />
    <None Include="packages.config" />
    <None Include="Scripts\jquery-3.3.1.intellisense.js" />
    <Content Include="Scripts\jquery-3.3.1.js" />
    <Content Include="Scripts\jquery-3.3.1.min.js" />
    <Content Include="Scripts\jquery-3.3.1.slim.js" />
    <Content Include="Scripts\jquery-3.3.1.slim.min.js" />
    <Content Include="Scripts\jquery-asyncUpload-0.1.js" />
    <Content Include="Scripts\jquery-hex-colorpicker.min.js" />
    <Content Include="Scripts\jquery-ui-1.12.1.js" />
    <Content Include="Scripts\jquery-ui-1.12.1.min.js" />
    <Content Include="Scripts\jquery-ui-i18n.min.js" />
    <Content Include="Scripts\jquery-ui-i18n.rainworx.js" />
    <Content Include="Scripts\jquery.cookie-1.4.1.min.js" />
    <Content Include="Scripts\jquery.cookie.js" />
    <Content Include="Scripts\jquery.doubleScroll.js" />
    <Content Include="Scripts\jquery.easing.1.3.js" />
    <Content Include="Scripts\jquery.everslider.js" />
    <Content Include="Scripts\jquery.everslider.min.js" />
    <Content Include="Scripts\jquery.mousewheel.js" />
    <Content Include="Scripts\jquery.numberformatter-1.2.4.js" />
    <Content Include="Scripts\jquery.ptTimeSelect.js" />
    <Content Include="Scripts\jquery.scrollUp.js" />
    <Content Include="Scripts\jquery.scrollUp.min.js" />
    <Content Include="Scripts\jquery.signalR-2.4.1.js" />
    <Content Include="Scripts\jquery.signalR-2.4.1.min.js" />
    <Content Include="Scripts\jquery.timepickr.min.js" />
    <Content Include="Scripts\jquery.timers.js" />
    <Content Include="Scripts\jquery.timers.min.js" />
    <Content Include="Scripts\jquery.ui.stars.js" />
    <Content Include="Scripts\jquery.ui.stars.min.js" />
    <Content Include="Scripts\jquery.ui.touch-punch.js" />
    <Content Include="Scripts\jquery.ui.touch-punch.min.js" />
    <Content Include="Scripts\json2.min.js" />
    <Content Include="Scripts\lightbox-2.6.min.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="Scripts\old-browser-alert.js" />
    <Content Include="Scripts\respimage.min.js" />
    <Content Include="Scripts\respond.min.js" />
    <Content Include="Views\Home\PeaceWarrior.cshtml" />
    <Content Include="Views\Shared\CategoryListTop.cshtml" />
    <Content Include="Views\Shared\LogOnUserControl.cshtml" />
    <Content Include="Scripts\jquery-3.3.1.slim.min.map" />
    <Content Include="Scripts\jquery-3.3.1.min.map" />
    <None Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\jshashtable-3.0.js" />
    <Content Include="Scripts\json2.js" />
    <Content Include="Scripts\MicrosoftAjax.debug.js" />
    <Content Include="Scripts\MicrosoftAjax.js" />
    <Content Include="Scripts\MicrosoftMvcAjax.debug.js" />
    <Content Include="Scripts\MicrosoftMvcAjax.js" />
    <Content Include="Scripts\MicrosoftMvcValidation.debug.js" />
    <Content Include="Scripts\MicrosoftMvcValidation.js" />
    <Content Include="Scripts\swfupload.js" />
    <Content Include="Scripts\ui.timepickr.min.js" />
    <Content Include="Views\Shared\AJAXUploadFile.cshtml" />
    <Content Include="Views\Shared\AJAXUploadFile_PDF.cshtml" />
    <Content Include="Views\Event\AJAXEventBannerUploader.cshtml" />
    <Content Include="Views\Event\AJAXEventImageUploader.cshtml" />
    <Content Include="Views\Account\ClosingGroups.cshtml" />
    <Content Include="Views\Event\EditLot.cshtml" />
    <Content Include="Views\Event\LotDetails.cshtml" />
    <Content Include="Views\Event\NotFound.cshtml" />
    <Content Include="Views\Event\LotDetail.cshtml" />
    <Content Include="Views\Event\EventSummary.cshtml" />
    <Content Include="Views\Event\AuctionDetails.cshtml" />
    <Content Include="Views\Account\SoftClosingGroups.cshtml" />
    <Content Include="Views\Event\Details.cshtml" />
    <Content Include="Views\Home\Maintenance.cshtml" />
    <Content Include="Views\Shared\PageOfListing_Grid.cshtml" />
    <Content Include="Views\Shared\PageOfEvents.cshtml" />
    <Content Include="Views\Shared\Event_Row.cshtml" />
    <Content Include="Views\Admin\AuctionLotSettings.cshtml" />
    <Content Include="Views\Account\SalesTransactionReport.cshtml" />
    <Content Include="Views\Page\Index.cshtml" />
    <Content Include="Views\Admin\SalesInvoicesReport.cshtml" />
    <Content Include="Views\Shared\RainWorx.FrameWorx.Providers.MediaAsset.Files.PDF_Main.cshtml" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Content\Site.css" />
    <Content Include="Scripts\_references.js" />
    <Content Include="Views\Web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\Shared\RegionCurrencySelector.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Shared\CategoryNavigator.cshtml" />
    <Content Include="Views\Shared\RandomLeftBanners.cshtml" />
    <Content Include="Views\Home\HomePageFeaturedListings.cshtml" />
    <Content Include="Views\Shared\AuctionThumbnail.cshtml" />
    <Content Include="Views\Shared\RainWorx.FrameWorx.Providers.MediaAsset.JPEGWithThumbnails_ThumbCrop.cshtml" />
    <Content Include="Views\Shared\RandomBottomBanners.cshtml" />
    <Content Include="Views\Account\LogOn.cshtml" />
    <Content Include="Views\Shared\LogOnBox.cshtml" />
    <Content Include="Views\Shared\RandomTopBanners.cshtml" />
    <Content Include="Views\Shared\RegionNavigator.cshtml" />
    <Content Include="Views\Listing\Details.cshtml" />
    <Content Include="Views\Shared\Action_BuyBox.cshtml" />
    <Content Include="Views\Shared\AuctionActionBox.cshtml" />
    <Content Include="Views\Listing\ListingDetail.cshtml" />
    <Content Include="Views\Shared\RainWorx.FrameWorx.Providers.MediaAsset.JPEGWithThumbnails_FullSize.cshtml" />
    <Content Include="Views\Shared\RainWorx.FrameWorx.Providers.MediaAsset.JPEGWithThumbnails_FullSizeLightbox.cshtml" />
    <Content Include="Views\Listing\AuctionDetails.cshtml" />
    <Content Include="Views\Shared\FeedbackRating.cshtml" />
    <Content Include="Views\Shared\AuctionPaymentOptions.cshtml" />
    <Content Include="Views\Home\About.cshtml" />
    <Content Include="Views\Shared\ClassifiedThumbnail.cshtml" />
    <Content Include="Views\Home\Contact.cshtml" />
    <Content Include="Views\Home\ContactUsSubmitted.cshtml" />
    <Content Include="Views\Shared\FixedPriceThumbnail.cshtml" />
    <Content Include="Views\Home\Help.cshtml" />
    <Content Include="Views\Home\PrivacyPolicy.cshtml" />
    <Content Include="Views\Home\Sitemap.cshtml" />
    <Content Include="Views\Home\Terms.cshtml" />
    <Content Include="Views\Home\Version.cshtml" />
    <Content Include="Web.sitemap" />
    <Content Include="Views\Shared\Address.cshtml" />
    <Content Include="Views\Shared\AddressEditor.cshtml" />
    <Content Include="Views\Shared\AuctionListingActions.cshtml" />
    <Content Include="Views\Shared\AuctionListingSummaryDetails.cshtml" />
    <Content Include="Views\Shared\BreadCrumbNavigator.cshtml" />
    <Content Include="Views\Shared\CategoryList.cshtml" />
    <Content Include="Views\Shared\CategoryListRoot.cshtml" />
    <Content Include="Views\Shared\CompactCategoryList.cshtml" />
    <Content Include="Views\Shared\CreditCard.cshtml" />
    <Content Include="Views\Shared\DemoLoginCallout.cshtml" />
    <Content Include="Views\Shared\FixedPriceListingActions.cshtml" />
    <Content Include="Views\Shared\FixedPriceListingSummaryDetails.cshtml" />
    <Content Include="Views\Shared\Listing_AuctionRow.cshtml" />
    <Content Include="Views\Shared\Listing_ClassifiedRow.cshtml" />
    <Content Include="Views\Shared\Listing_FixedPriceRow.cshtml" />
    <Content Include="Views\Shared\Listing_HeaderRow.cshtml" />
    <Content Include="Views\Shared\Listing_Row_Shipping.cshtml" />
    <Content Include="Views\Shared\PageOfListing.cshtml" />
    <Content Include="Views\Shared\PaymentHistory.cshtml" />
    <Content Include="Views\Shared\PropertyGroup.cshtml" />
    <Content Include="Views\Shared\RainWorx.FrameWorx.Providers.MediaAsset.JPEGWithThumbnails_ThumbCrop_for_ListingDetail.cshtml" />
    <Content Include="Views\Shared\RainWorx.FrameWorx.Providers.MediaAsset.JPEGWithThumbnails_ThumbFit.cshtml" />
    <Content Include="Views\Shared\RainWorx.FrameWorx.Providers.MediaAsset.JPEGWithThumbnails_ThumbFit_for_ListingDetail.cshtml" />
    <Content Include="Views\Shared\RainWorx.FrameWorx.Providers.MediaAsset.YouTube_Main.cshtml" />
    <Content Include="Views\Shared\SellersPaymentSettings.cshtml" />
    <Content Include="Views\Shared\SellersTaxRates.cshtml" />
    <Content Include="Views\Admin\_AdminLayout.cshtml" />
    <Content Include="Views\Shared\AJAXCategoryChooser.cshtml" />
    <Content Include="Views\Shared\AJAXCustomFieldSearcher.cshtml" />
    <Content Include="Views\Shared\AJAXImageUploader.cshtml" />
    <Content Include="Views\Shared\AJAXListingTypeChooser.cshtml" />
    <Content Include="Views\Shared\AJAXListingTypeSearcher.cshtml" />
    <Content Include="Views\Shared\AJAXRegionChooser.cshtml" />
    <Content Include="Views\Shared\AJAXYouTubeVideoReferenceUploader.cshtml" />
    <Content Include="Views\Shared\AuctionActionConfirmationBox.cshtml" />
    <Content Include="Views\Shared\AuctionBuyerFees.cshtml" />
    <Content Include="Views\Shared\AuctionListingFields.cshtml" />
    <Content Include="Views\Listing\Browse.cshtml" />
    <Content Include="Views\Shared\ClassifiedActionBox.cshtml" />
    <Content Include="Views\Shared\ClassifiedActionConfirmationBox.cshtml" />
    <Content Include="Views\Shared\ClassifiedBuyerFees.cshtml" />
    <Content Include="Views\Listing\ClassifiedDetails.cshtml" />
    <Content Include="Views\Shared\ClassifiedListingFields.cshtml" />
    <Content Include="Views\Shared\ClassifiedPaymentOptions.cshtml" />
    <Content Include="Views\Listing\ConfirmAction.cshtml" />
    <Content Include="Views\Shared\ContextualStatus.cshtml" />
    <Content Include="Views\Listing\CreateListingPage1.cshtml" />
    <Content Include="Views\Listing\CreateListingPage2.cshtml" />
    <Content Include="Views\Shared\DecorationsChooser.cshtml" />
    <Content Include="Views\Shared\DecorationsChooserEnableOnly.cshtml" />
    <Content Include="Views\Listing\DurationOptional.cshtml" />
    <Content Include="Views\Listing\DurationPartial.cshtml" />
    <Content Include="Views\Listing\Edit.cshtml" />
    <Content Include="Views\Shared\FixedPriceActionBox.cshtml" />
    <Content Include="Views\Shared\FixedPriceActionConfirmationBox.cshtml" />
    <Content Include="Views\Shared\FixedPriceBuyerFees.cshtml" />
    <Content Include="Views\Listing\FixedPriceDetails.cshtml" />
    <Content Include="Views\Shared\FixedPriceListingFields.cshtml" />
    <Content Include="Views\Shared\FixedPricePaymentOptions.cshtml" />
    <Content Include="Views\Listing\History.cshtml" />
    <Content Include="Views\Listing\ListingConfirmation.cshtml" />
    <Content Include="Views\Shared\LocationsChooser.cshtml" />
    <Content Include="Views\Shared\LocationsChooserEnableOnly.cshtml" />
    <Content Include="Views\Shared\NoPermissionToSell.cshtml" />
    <Content Include="Views\Listing\NotFound.cshtml" />
    <Content Include="Views\Shared\PaymentAndTaxDetails.cshtml" />
    <Content Include="Views\Listing\PostAndFinalFees.cshtml" />
    <Content Include="Views\Shared\Search.cshtml" />
    <Content Include="Views\Shared\ShippingMethodChooser.cshtml" />
    <Content Include="Views\Account\_MyAccountLayout.cshtml" />
    <Content Include="Views\Account\AccountSettings.cshtml" />
    <Content Include="Views\Account\Summary.cshtml" />
    <Content Include="Views\Account\BiddingActive.cshtml" />
    <Content Include="Views\Account\Action_Buyer.cshtml" />
    <Content Include="Views\Account\Action_BuyerBought.cshtml" />
    <Content Include="Views\Account\Action_Seller.cshtml" />
    <Content Include="Views\Account\Action_SellerEndedWithSuccess.cshtml" />
    <Content Include="Views\Account\Action_SellerNoSale.cshtml" />
    <Content Include="Views\Account\Action_SellerPending.cshtml" />
    <Content Include="Views\Account\Action_SellerSold.cshtml" />
    <Content Include="Views\Account\AddCreditCard.cshtml" />
    <Content Include="Views\Account\AddressManagement.cshtml" />
    <Content Include="Views\Account\AddressSelector.cshtml" />
    <Content Include="Views\Account\AJAXNotifyPayment.cshtml" />
    <Content Include="Views\Account\AuctionBiddingRow.cshtml" />
    <Content Include="Views\Account\AuthorizeNetAimInvoice_Buyer.cshtml" />
    <Content Include="Views\Account\BackButton.cshtml" />
    <Content Include="Views\Account\BiddingNotWon.cshtml" />
    <Content Include="Views\Account\BiddingWatching.cshtml" />
    <Content Include="Views\Account\BiddingWon.cshtml" />
    <Content Include="Views\Account\BuyerFeedback.cshtml" />
    <Content Include="Views\Account\ChangePassword.cshtml" />
    <Content Include="Views\Account\CreateAddress.cshtml" />
    <Content Include="Views\Account\CreditCards.cshtml" />
    <Content Include="Views\Account\EditAddress.cshtml" />
    <Content Include="Views\Account\Feedback.cshtml" />
    <Content Include="Views\Account\Fees.cshtml" />
    <Content Include="Views\Account\ForgotPassword.cshtml" />
    <Content Include="Views\Account\ForgotPasswordEmailSent.cshtml" />
    <Content Include="Views\Account\FreeFormInvoice_Buyer.cshtml" />
    <Content Include="Views\Account\FreeFormInvoice_Seller.cshtml" />
    <Content Include="Views\Account\GetCSVReport.cshtml" />
    <Content Include="Views\Account\HistoricalFees.cshtml" />
    <Content Include="Views\Account\InitialAddress.cshtml" />
    <Content Include="Views\Account\InvoiceDetail.cshtml" />
    <Content Include="Views\Account\InvoicePurchases.cshtml" />
    <Content Include="Views\Account\InvoiceSales.cshtml" />
    <Content Include="Views\Account\LineItem_BuyerBoughtRow.cshtml" />
    <Content Include="Views\Account\LineItem_HeaderRow.cshtml" />
    <Content Include="Views\Account\LineItem_SellerSoldRow.cshtml" />
    <Content Include="Views\Account\Listing_ActiveAuctionRow.cshtml" />
    <Content Include="Views\Account\Listing_ActiveClassifiedRow.cshtml" />
    <Content Include="Views\Account\Listing_ActiveFixedPriceRow.cshtml" />
    <Content Include="Views\Account\Listing_BuyerNoSaleRow.cshtml" />
    <Content Include="Views\Account\Listing_PendingAuctionRow.cshtml" />
    <Content Include="Views\Account\Listing_PendingClassifiedRow.cshtml" />
    <Content Include="Views\Account\Listing_PendingFixedPriceRow.cshtml" />
    <Content Include="Views\Account\Listing_SellerEndedRow.cshtml" />
    <Content Include="Views\Account\Listing_SellerNoSaleRow.cshtml" />
    <Content Include="Views\Account\ListingsActive.cshtml" />
    <Content Include="Views\Account\ListingsEnded.cshtml" />
    <Content Include="Views\Account\ListingsPending.cshtml" />
    <Content Include="Views\Account\ListingsSuccessful.cshtml" />
    <Content Include="Views\Account\ListingsUnsuccessful.cshtml" />
    <Content Include="Views\Account\MyPaymentSettings_Inline.cshtml" />
    <Content Include="Views\Account\MyTaxRates_Inline.cshtml" />
    <Content Include="Views\Account\PageOfFees.cshtml" />
    <Content Include="Views\Account\PageOfWatching.cshtml" />
    <Content Include="Views\Account\PaidFeeInvoice.cshtml" />
    <Content Include="Views\Account\PaidListingInvoice.cshtml" />
    <Content Include="Views\Account\PayPalStandardInvoice_Buyer.cshtml" />
    <Content Include="Views\Account\PropertyManagement.cshtml" />
    <Content Include="Views\Account\PropertyManagement_Inline.cshtml" />
    <Content Include="Views\Account\ReadMessage.cshtml" />
    <Content Include="Views\Account\Register.cshtml" />
    <Content Include="Views\Account\ResetPassword.cshtml" />
    <Content Include="Views\Account\SalesTaxManagement.cshtml" />
    <Content Include="Views\Account\SalesTaxManagement_Inline.cshtml" />
    <Content Include="Views\Account\SellerFeedback.cshtml" />
    <Content Include="Views\Account\SendListingMessage.cshtml" />
    <Content Include="Views\Account\SetBillingAddress.cshtml" />
    <Content Include="Views\Account\SetShippingAddress.cshtml" />
    <Content Include="Views\Account\SimilarLineItems.cshtml" />
    <Content Include="Views\Account\SubmitFeedback.cshtml" />
    <Content Include="Views\Account\UnpaidFeeInvoice.cshtml" />
    <Content Include="Views\Account\UnpaidListingInvoice.cshtml" />
    <Content Include="Views\Account\UserVerification.cshtml" />
    <Content Include="Views\Account\ViewBuyerFeedback.cshtml" />
    <Content Include="Views\Account\ViewFeedback.cshtml" />
    <Content Include="Views\Account\ViewFeedback_Summary.cshtml" />
    <Content Include="Views\Account\ViewFeedbackForOthers.cshtml" />
    <Content Include="Views\Account\ViewFeedbackList_FromOthers.cshtml" />
    <Content Include="Views\Account\ViewFeedbackList_ToOthers.cshtml" />
    <Content Include="Views\Account\ViewMessages.cshtml" />
    <Content Include="Views\Account\ViewSellerFeedback.cshtml" />
    <Content Include="Views\Account\Watching_AuctionRow.cshtml" />
    <Content Include="Views\Account\Watching_ClassifiedRow.cshtml" />
    <Content Include="Views\Account\Watching_FixedPriceRow.cshtml" />
    <Content Include="Views\Admin\Summary.cshtml" />
    <Content Include="Views\Admin\AddAddress.cshtml" />
    <Content Include="Views\Admin\AddCreditCard.cshtml" />
    <Content Include="Views\Admin\Attributes.cshtml" />
    <Content Include="Views\Admin\AuthorizeNetAimInvoice_Buyer.cshtml" />
    <Content Include="Views\Admin\BackButton.cshtml" />
    <Content Include="Views\Admin\BannerHtmlEditor.cshtml" />
    <Content Include="Views\Admin\BannerImageUploader.cshtml" />
    <Content Include="Views\Admin\Banners.cshtml" />
    <Content Include="Views\Admin\CategoryDetail.cshtml" />
    <Content Include="Views\Admin\CategoryEditor.cshtml" />
    <Content Include="Views\Admin\CheckSite.cshtml" />
    <Content Include="Views\Admin\ContentEditor.cshtml" />
    <Content Include="Views\Admin\ContentManagement.cshtml" />
    <Content Include="Views\Admin\CreateField.cshtml" />
    <Content Include="Views\Admin\CreateUser.cshtml" />
    <Content Include="Views\Admin\CreditCards.cshtml" />
    <Content Include="Views\Admin\CultureManagement.cshtml" />
    <Content Include="Views\Admin\CurrencyManagement.cshtml" />
    <Content Include="Views\Admin\DecorationsManagement.cshtml" />
    <Content Include="Views\Admin\EditAddress.cshtml" />
    <Content Include="Views\Admin\EditDecoration.cshtml" />
    <Content Include="Views\Admin\EditEventFees.cshtml" />
    <Content Include="Views\Admin\EditFees.cshtml" />
    <Content Include="Views\Admin\EditFeeTier.cshtml" />
    <Content Include="Views\Admin\EditField.cshtml" />
    <Content Include="Views\Admin\EditLocation.cshtml" />
    <Content Include="Views\Admin\EditUser.cshtml" />
    <Content Include="Views\Admin\EmailTemplates.cshtml" />
    <Content Include="Views\Admin\EventFeeManagement.cshtml" />
    <Content Include="Views\Admin\EventLog.cshtml" />
    <Content Include="Views\Admin\ExportUserCSV.cshtml" />
    <Content Include="Views\Admin\ExtendListings.cshtml" />
    <Content Include="Views\Admin\FavIconUploader.cshtml" />
    <Content Include="Views\Admin\FeedbackRating.cshtml" />
    <Content Include="Views\Admin\Fields.cshtml" />
    <Content Include="Views\Admin\ImportCSV.cshtml" />
    <Content Include="Views\Admin\ImportCSVHelp.cshtml" />
    <Content Include="Views\Admin\IncrementManagement.cshtml" />
    <Content Include="Views\Admin\InitialAddress.cshtml" />
    <Content Include="Views\Admin\InlineMultiCategoryChooser.cshtml" />
    <Content Include="Views\Admin\InlineMultiRegionChooser.cshtml" />
    <Content Include="Views\Admin\InvoiceDetail.cshtml" />
    <Content Include="Views\Admin\LanguageManagement.cshtml" />
    <Content Include="Views\Admin\ListingFeesRevenueReport.cshtml" />
    <Content Include="Views\Admin\ListingTypeBidIncrements.cshtml" />
    <Content Include="Views\Admin\ListingTypeProperties.cshtml" />
    <Content Include="Views\Admin\ListingTypeToggle.cshtml" />
    <Content Include="Views\Admin\LogoUploader.cshtml" />
    <Content Include="Views\Admin\Maintenance.cshtml" />
    <Content Include="Views\Admin\NewSiteFeesReport.cshtml" />
    <Content Include="Views\Admin\PageOfFees.cshtml" />
    <Content Include="Views\Admin\PaidFeeInvoice.cshtml" />
    <Content Include="Views\Admin\PaidListingInvoice.cshtml" />
    <Content Include="Views\Admin\PayPalStandardInvoice_Buyer.cshtml" />
    <Content Include="Views\Admin\PropertyGroup.cshtml" />
    <Content Include="Views\Admin\PropertyManagement.cshtml" />
    <Content Include="Views\Admin\RegionDetail.cshtml" />
    <Content Include="Views\Admin\RegionEditor.cshtml" />
    <Content Include="Views\Admin\RootCategoryEditor.cshtml" />
    <Content Include="Views\Admin\SalesTransactionReport.cshtml" />
    <Content Include="Views\Admin\ScheduledPayments.cshtml" />
    <Content Include="Views\Admin\ShippingMethodEditor.cshtml" />
    <Content Include="Views\Admin\ShippingMethods.cshtml" />
    <Content Include="Views\Admin\SimilarLineItems.cshtml" />
    <Content Include="Views\Admin\SiteCheck.cshtml" />
    <Content Include="Views\Admin\SiteFeesReport.cshtml" />
    <Content Include="Views\Admin\UnpaidFeeInvoice.cshtml" />
    <Content Include="Views\Admin\UnpaidListingInvoice.cshtml" />
    <Content Include="Views\Admin\UserAddresses.cshtml" />
    <Content Include="Views\Admin\UserCreditCards.cshtml" />
    <Content Include="Views\Admin\UserFeedback.cshtml" />
    <Content Include="Views\Admin\UserFeedbackList_FromOthers.cshtml" />
    <Content Include="Views\Admin\UserFeedbackList_ToOthers.cshtml" />
    <Content Include="Views\Admin\UserManagement.cshtml" />
    <Content Include="Views\Admin\UserManagementTopNav.cshtml" />
    <Content Include="Views\Admin\UserSummary.cshtml" />
    <Content Include="Views\Admin\VersionInfo.cshtml" />
    <Content Include="Views\Shared\_BlankLayout.cshtml" />
    <Content Include="Views\Shared\FixedPriceCurrentQuantity.cshtml" />
    <Content Include="Views\Admin\LogOnUserControl.cshtml" />
    <Content Include="Views\Admin\CategoryListRoot.cshtml" />
    <Content Include="Views\Admin\CategoryList.cshtml" />
    <Content Include="Views\Shared\_NoLayout.cshtml" />
    <Content Include="Views\Admin\AddressEditor.cshtml" />
    <Content Include="Views\Admin\MobileLogoUploader.cshtml" />
    <Content Include="Views\Admin\RegionCurrencySelector.cshtml" />
    <Content Include="Views\Admin\CountryManagement.cshtml" />
    <Content Include="Views\Admin\EmailTemplateEditor.cshtml" />
    <Content Include="Views\Shared\PrintInvoice.cshtml" />
    <Content Include="Views\Shared\UnpaidFeeInvoice_ForPrinting.cshtml" />
    <Content Include="Views\Shared\UnpaidListingInvoice_ForPrinting.cshtml" />
    <Content Include="Views\Shared\PaidFeeInvoice_ForPrinting.cshtml" />
    <Content Include="Views\Shared\PaidListingInvoice_ForPrinting.cshtml" />
    <Content Include="Views\Admin\StatesRegionsManagement.cshtml" />
    <Content Include="Views\Admin\PreviewEmailTemplate.cshtml" />
    <Content Include="Views\Account\AdminBypassInvoice_Buyer.cshtml" />
    <Content Include="Views\Event\CreateLotPage1.cshtml" />
    <Content Include="Views\Event\CreateLotPage2.cshtml" />
    <Content Include="Views\Admin\ImportUsers.cshtml" />
    <Content Include="Views\Home\NotFound.cshtml" />
    <Content Include="Views\Admin\SetBillingAddress.cshtml" />
    <Content Include="Views\Admin\SetShippingAddress.cshtml" />
    <Content Include="Views\Listing\ConfirmActionModal.cshtml" />
    <Content Include="Views\Shared\_EmptyLayout.cshtml" />
    <Content Include="Views\Admin\DataMaintenance.cshtml" />
    <Content Include="Views\Account\PaymentReceived.cshtml" />
    <Content Include="Views\Account\SendUserMessage.cshtml" />
    <Content Include="Views\Admin\ImportListings.cshtml" />
    <Content Include="Views\Admin\ImportCategories.cshtml" />
    <Content Include="Views\Admin\ImportRegions.cshtml" />
    <Content Include="Views\Shared\SignalRHandler.cshtml" />
    <Content Include="Views\Event\CreateEvent.cshtml" />
    <Content Include="Views\Event\EventConfirmation.cshtml" />
    <Content Include="Views\Event\EndTimeHistory.cshtml" />
    <Content Include="Views\Event\Edit.cshtml" />
    <Content Include="Views\Home\HomepageEvents.cshtml" />
    <Content Include="Views\Account\EventsPublished.cshtml" />
    <Content Include="Views\Account\Event_Row.cshtml" />
    <Content Include="Views\Account\Action_Events.cshtml" />
    <Content Include="Views\Event\LotConfirmation.cshtml" />
    <Content Include="Views\Account\ListingsDrafts.cshtml" />
    <Content Include="Views\Account\Action_SellerDraft.cshtml" />
    <Content Include="Views\Account\Listing_SellerDraftRow.cshtml" />
    <Content Include="Views\Account\EventsArchived.cshtml" />
    <Content Include="Views\Account\EventsClosed.cshtml" />
    <Content Include="Views\Account\EventsDrafts.cshtml" />
    <Content Include="Views\Account\LotsByEvent.cshtml" />
    <Content Include="Views\Account\Lot_Row.cshtml" />
    <Content Include="Views\Account\Event_Row_Draft.cshtml" />
    <Content Include="Views\Account\Event_Row_Published.cshtml" />
    <Content Include="Views\Shared\Event_StatusLabel.cshtml" />
    <Content Include="Views\Account\Action_Lot.cshtml" />
    <Content Include="Views\Shared\Listing_StatusLabel.cshtml" />
    <Content Include="Views\Shared\NoPhoto.cshtml" />
    <Content Include="Views\Shared\NoPhotoThumbnail.cshtml" />
    <Content Include="Views\Event\OptionsDropDown_Event.cshtml" />
    <Content Include="Views\Event\LotNotFound.cshtml" />
    <Content Include="Views\Account\Action_Events_Quick.cshtml" />
    <Content Include="Views\Event\EventNeeded.cshtml" />
    <Content Include="Views\Account\CopyLots.cshtml" />
    <Content Include="Views\Admin\Taxes.cshtml" />
    <Content Include="Views\Account\PendingFeeInvoice.cshtml" />
    <Content Include="Views\Account\PendingListingInvoice.cshtml" />
    <Content Include="Views\Admin\PendingFeeInvoice.cshtml" />
    <Content Include="Views\Admin\PendingListingInvoice.cshtml" />
    <Content Include="Views\Shared\PendingFeeInvoice_ForPrinting.cshtml" />
    <Content Include="Views\Shared\PendingListingInvoice_ForPrinting.cshtml" />
    <Content Include="Views\Admin\FeeCategories.cshtml" />
    <Content Include="Views\Shared\PublicQandA.cshtml" />
    <Content Include="Views\Account\EventSummary.cshtml" />
    <Content Include="Views\Event\AJAXUploadEventFile.cshtml" />
    <Content Include="Views\Shared\PrintMultipleInvoices.cshtml" />
    <Content Include="Views\Account\StripeConnectInvoice_Buyer.cshtml" />
    <Content Include="Views\Shared\StripeConnectionLinks.cshtml" />
    <Content Include="Views\Shared\RegionListTop.cshtml" />
    <Content Include="Views\Shared\RegionListTop.cshtml" />
    <Content Include="Views\Admin\StripeConnectInvoice_Buyer.cshtml" />
    <Content Include="Views\Shared\DemoMenu.cshtml" />
    <Content Include="Views\Account\InvoiceEventSales.cshtml" />
    <Content Include="Views\Account\EventSalesTransactionReport.cshtml" />
    <Content Include="Views\Listing\OptionsMenu.cshtml" />
    <Content Include="Views\Shared\ContextMenu.cshtml" />
    <Content Include="Views\Shared\ListingDetail_SellerUserSummary.cshtml" />
    <Content Include="Views\Shared\BuyerFee.cshtml" />
    <Content Include="Views\Shared\ShippingOptionDisplay.cshtml" />
    <Content Include="Views\Shared\ListingDetail_AddWatch.cshtml" />
    <Content Include="Views\Shared\ListingDetail_GeneralData.cshtml" />
    <Content Include="Views\Listing\MakeOfferModal.cshtml" />
    <Content Include="Views\Shared\MakeOfferForm.cshtml" />
    <Content Include="Views\Listing\ConfirmOfferModal.cshtml" />
    <Content Include="Views\Listing\ManageOffers.cshtml" />
    <Content Include="Views\Account\BiddingOffers.cshtml" />
    <Content Include="Views\Account\Offer_Row.cshtml" />
    <Content Include="Views\Shared\AuctionOfferHistory.cshtml" />
    <Content Include="Views\Shared\FixedPriceOfferHistory.cshtml" />
    <Content Include="Views\Event\OptionsMenu.cshtml" />
    <Content Include="Views\Shared\Lockout.cshtml" />
    <Content Include="Views\Admin\ImportFeedbacks.cshtml" />
    <Content Include="Views\Admin\EventActivityReport.cshtml" />
    <Content Include="Views\Shared\Listing_StatusLabel_ForSeller.cshtml" />
    <Content Include="Views\Event\Index.cshtml" />
    <Content Include="Views\Event\PageOfListing_EventDetailGrid.cshtml" />
    <Content Include="Views\Event\RollingLots_List.cshtml" />
    <Content Include="Views\Event\RollingLots_Grid.cshtml" />
    <Content Include="Views\Event\MoreLots_Inline.cshtml" />
    <Content Include="Views\Shared\AuctionDecreaseReserveOnly.cshtml" />
    <Content Include="Views\Shared\AuctionPreviewBox.cshtml" />
    <Content Include="Views\Shared\FixedPricePreviewBox.cshtml" />
    <Content Include="Views\Shared\ClassifiedPreviewBox.cshtml" />
    <Content Include="Views\Shared\AJAXCategoryChooserForListingType.cshtml" />
    <Content Include="Views\Shared\AuctionReadOnlyReserve.cshtml" />
    <Content Include="Views\Shared\UpgradeUsers.cshtml" />
    <Content Include="Views\Admin\EventLogHistogram.cshtml" />
    <Content Include="Views\Shared\AuctionInlineBidding_Includes.cshtml" />
    <Content Include="Views\Shared\Listing_AuctionRow_WithBidding.cshtml" />
    <Content Include="Views\Shared\AuctionThumbnail_WithBidding.cshtml" />
    <Content Include="Views\Account\AuctionBiddingRow_WithBidding.cshtml" />
    <Content Include="Views\Account\Watching_AuctionRow_WithBidding.cshtml" />
    <Content Include="Views\Event\EventNeededForNewLot.cshtml" />
    <Content Include="Views\Admin\SalesPreferences.cshtml" />
    <Content Include="Views\Account\StripeCardManagement.cshtml" />
    <Content Include="Views\Account\AddStripeCard.cshtml" />
    <Content Include="Views\Account\TestCardAuthOnly.cshtml" />
    <Content Include="Views\Admin\Reports.cshtml" />
    <Content Include="Views\Admin\DemoMenu.cshtml" />
    <Content Include="Views\Account\ConsignorFees.cshtml" />
    <Content Include="Views\Account\ConsignorStatements.cshtml" />
    <Content Include="Views\Account\ConsignorManagement.cshtml" />
    <Content Include="Views\Account\CategoryListRoot.cshtml" />
    <Content Include="Views\Account\CategoryList.cshtml" />
    <Content Include="Views\Account\AddConsignor.cshtml" />
    <Content Include="Views\Account\Action_Consignor.cshtml" />
    <Content Include="Views\Account\EditConsignor.cshtml" />
    <Content Include="Views\Shared\AJAXConsignorChooser.cshtml" />
    <Content Include="Views\Shared\PrintStatement.cshtml" />
    <Content Include="Views\Shared\AJAXSellerLogoUploader.cshtml" />
    <Content Include="Views\Shared\PrintMultipleStatements.cshtml" />
    <Content Include="Views\Shared\Statement_ForPrinting.cshtml" />
    <Content Include="Views\Account\StripeConnectInvoice_BuyerActionNeeded.cshtml" />
    <Content Include="Views\Admin\StripeConnectInvoice_BuyerActionNeeded.cshtml" />
    <Content Include="Views\Admin\ConsignorStatementsReport.cshtml" />
    <Content Include="Views\Admin\ConsignorManagement.cshtml" />
    <Content Include="Views\Admin\AddConsignor.cshtml" />
    <Content Include="Views\Admin\EditConsignor.cshtml" />
    <Content Include="Views\Admin\ConsignorFees.cshtml" />
    <Content Include="Views\Admin\Action_Consignor.cshtml" />
    <Content Include="Views\Shared\HeaderNavigation_Style01.cshtml" />
    <Content Include="Views\Shared\HeaderNavigation_Style02.cshtml" />
    <Content Include="Views\Account\AddConsignorModal.cshtml" />
    <Content Include="Views\Account\Statement.cshtml" />
    <Content Include="Views\Admin\Statement.cshtml" />
    <Content Include="Views\Shared\AJAXSellerChooser.cshtml" />
    <Content Include="Views\Account\ConsignmentItems.cshtml" />
    <Content Include="Views\Account\Listing_ConsignmentRow.cshtml" />
    <Content Include="Views\Account\Action_ConsignmentActive.cshtml" />
    <Content Include="Views\Account\Action_ConsignmentClosed.cshtml" />
    <Content Include="Views\Account\ConsignmentClosedItems.cshtml" />
    <Content Include="Views\Account\LineItem_ConsignmentRow.cshtml" />
    <Content Include="Views\Account\ConsignmentStatements.cshtml" />
    <Content Include="Views\Admin\ConsignmentSalesReport.cshtml" />
    <Content Include="Views\Account\ConsignmentSalesReport.cshtml" />
    <Content Include="Views\Event\LotEnded.cshtml" />
    <Content Include="Views\Admin\AssignNewFee.cshtml" />
    <Content Include="Views\Shared\AJAXUserChooser.cshtml" />
    <Content Include="Views\Account\ConsignmentItem_Row.cshtml" />
    <Content Include="Views\Account\ConsignmentClosedItem_Row.cshtml" />
    <Content Include="Views\Account\ClosingGroups_Fancy.cshtml" />
    <Content Include="Views\Account\ClosingGroups_Simple.cshtml" />
    <Content Include="Views\Account\SoftClosingGroups_Fancy.cshtml" />
    <Content Include="Views\Account\SoftClosingGroups_Simple.cshtml" />
    <Content Include="Views\Account\ActiveConsignments.cshtml" />
    <Content Include="Views\Admin\Address.cshtml" />
    <Content Include="Views\Event\EventEnded.cshtml" />
    <Content Include="Views\Shared\AJAXConsignorChooserHidden.cshtml" />
    <Content Include="Views\Shared\HomeCategoryNavigator.cshtml" />
    <Content Include="Views\Home\HomePageHotListings.cshtml" />
    <Content Include="Views\Home\Career.cshtml" />
    <Content Include="Views\Home\Wholesale.cshtml" />
    <Content Include="Views\Home\HowItWork.cshtml" />
    <Content Include="Views\Home\Dummy2.cshtml" />
    <Content Include="Views\Home\CareerSubmitted.cshtml" />
    <Content Include="Views\Shared\LogOffUserControl.cshtml" />
    <Content Include="Views\Listing\_ContentBlockerListingModal.cshtml" />
    <Content Include="Views\Home\home_slider.cshtml" />
    <Content Include="Views\Admin\AJAXCategoryImage.cshtml" />
    <Content Include="Views\Admin\SliderImages.cshtml" />
    <Content Include="Views\Account\ListingsSellerApproval.cshtml" />
    <Content Include="Views\Account\Listing_SellerPendingDraftRow.cshtml" />
    <Content Include="Views\Account\ListingsModeratorApproval.cshtml" />
    <Content Include="Views\Account\Listing_SellerPendingApprovalDraftRow.cshtml" />
    <Content Include="Views\Account\Action_SellerPendingApprovalDraft.cshtml" />
    <Content Include="Views\Account\Action_MediatorPendingApprovalDraft.cshtml" />
    <Content Include="Views\Account\Listing_MediatorPendingApprovalDraftRow.cshtml" />
    <Content Include="Views\Home\Glossary.cshtml" />
    <Content Include="Views\Home\MPExclusive.cshtml" />
    <Content Include="Views\Home\CommunityGuidelines.cshtml" />
    <Content Include="Views\Listing\CustomImageUploaderAjax.cshtml" />
    <Content Include="Views\Account\SendInvoiceMessage.cshtml" />
    <Content Include="Views\Home\Vaults.cshtml" />
    <Content Include="Views\Shared\RightBoxCharity.cshtml" />
    <Content Include="Views\Shared\Disclaimer.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Content\Images\Banners\" />
    <Folder Include="Content\sliderImages\" />
    <Folder Include="Models\ResponseModels\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_GlobalResources\ShippingOptions.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>ShippingOptions.Designer.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_GlobalResources\AdminStrings.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>AdminStrings.designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="App_GlobalResources\AuctionListing.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>AuctionListing.designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="App_GlobalResources\ClassifiedListing.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>ClassifiedListing.designer.cs</LastGenOutput>
    </Content>
    <Content Include="App_GlobalResources\CustomFields.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>CustomFields.designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="App_GlobalResources\FixedPriceListing.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>FixedPriceListing.designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </Content>
    <Content Include="App_GlobalResources\GlobalStrings.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <SubType>Designer</SubType>
      <LastGenOutput>GlobalStrings.designer.cs</LastGenOutput>
    </Content>
    <Content Include="App_GlobalResources\Licensing.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>Licensing.designer.cs</LastGenOutput>
    </Content>
    <Content Include="App_GlobalResources\Validation.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>Validation.designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_GlobalResources\Countries.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>Countries.Designer.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_GlobalResources\States.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>States.Designer.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_GlobalResources\AriaStrings.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>AriaStrings.Designer.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_GlobalResources\TimeZones.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>TimeZones.Designer.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="App_GlobalResources\CategoriesRegions.resx">
      <Generator>GlobalResourceProxyGenerator</Generator>
      <LastGenOutput>CategoriesRegions.designer.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Provider Packages\Invoice Logic Provider\Source\RainWorx.FrameWorx.Providers.InvoiceLogic.csproj">
      <Project>{2516a3bc-2c8a-45f5-9c71-95963c56abce}</Project>
      <Name>RainWorx.FrameWorx.Providers.InvoiceLogic</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>64480</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:64580/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>