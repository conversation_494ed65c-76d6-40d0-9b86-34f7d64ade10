﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel.Annotations</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.DataAnnotations.AssociationAttribute">
      <summary>Specifies that an entity member represents a data relationship, such as a foreign key relationship.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.AssociationAttribute.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.AssociationAttribute" /> class.</summary>
      <param name="name">The name of the association. </param>
      <param name="thisKey">A comma-separated list of the property names of the key values on the <paramref name="thisKey" /> side of the association.</param>
      <param name="otherKey">A comma-separated list of the property names of the key values on the <paramref name="otherKey" /> side of the association.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.IsForeignKey">
      <summary>Gets or sets a value that indicates whether the association member represents a foreign key.</summary>
      <returns>true if the association represents a foreign key; otherwise, false.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.Name">
      <summary>Gets the name of the association.</summary>
      <returns>The name of the association.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.OtherKey">
      <summary>Gets the property names of the key values on the OtherKey side of the association.</summary>
      <returns>A comma-separated list of the property names that represent the key values on the OtherKey side of the association.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.OtherKeyMembers">
      <summary>Gets a collection of individual key members that are specified in the <see cref="P:System.ComponentModel.DataAnnotations.AssociationAttribute.OtherKey" /> property.</summary>
      <returns>A collection of individual key members that are specified in the <see cref="P:System.ComponentModel.DataAnnotations.AssociationAttribute.OtherKey" /> property.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.ThisKey">
      <summary>Gets the property names of the key values on the ThisKey side of the association.</summary>
      <returns>A comma-separated list of the property names that represent the key values on the ThisKey side of the association.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.AssociationAttribute.ThisKeyMembers">
      <summary>Gets a collection of individual key members that are specified in the <see cref="P:System.ComponentModel.DataAnnotations.AssociationAttribute.ThisKey" /> property.</summary>
      <returns>A collection of individual key members that are specified in the <see cref="P:System.ComponentModel.DataAnnotations.AssociationAttribute.ThisKey" /> property.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.CompareAttribute">
      <summary>Provides an attribute that compares two properties.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CompareAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.CompareAttribute" /> class.</summary>
      <param name="otherProperty">The property to compare with the current property.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CompareAttribute.FormatErrorMessage(System.String)">
      <summary>Applies formatting to an error message, based on the data field where the error occurred.</summary>
      <returns>The formatted error message.</returns>
      <param name="name">The name of the field that caused the validation failure.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CompareAttribute.IsValid(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>Determines whether a specified object is valid.</summary>
      <returns>true if <paramref name="value" /> is valid; otherwise, false.</returns>
      <param name="value">The object to validate.</param>
      <param name="validationContext">An object that contains information about the validation request.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CompareAttribute.OtherProperty">
      <summary>Gets the property to compare with the current property.</summary>
      <returns>The other property.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CompareAttribute.OtherPropertyDisplayName">
      <summary>Gets the display name of the other property.</summary>
      <returns>The display name of the other property.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CompareAttribute.RequiresValidationContext">
      <summary>Gets a value that indicates whether the attribute requires validation context.</summary>
      <returns>true if the attribute requires validation context; otherwise, false.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ConcurrencyCheckAttribute">
      <summary>Specifies that a property participates in optimistic concurrency checks.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ConcurrencyCheckAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ConcurrencyCheckAttribute" /> class.</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.CreditCardAttribute">
      <summary>Specifies that a data field value is a credit card number.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CreditCardAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.CreditCardAttribute" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CreditCardAttribute.IsValid(System.Object)">
      <summary>Determines whether the specified credit card number is valid. </summary>
      <returns>true if the credit card number is valid; otherwise, false.</returns>
      <param name="value">The value to validate.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.CustomValidationAttribute">
      <summary>Specifies a custom validation method that is used to validate a property or class instance.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CustomValidationAttribute.#ctor(System.Type,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.CustomValidationAttribute" /> class.</summary>
      <param name="validatorType">The type that contains the method that performs custom validation.</param>
      <param name="method">The method that performs custom validation.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.CustomValidationAttribute.FormatErrorMessage(System.String)">
      <summary>Formats a validation error message.</summary>
      <returns>An instance of the formatted error message.</returns>
      <param name="name">The name to include in the formatted message.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CustomValidationAttribute.Method">
      <summary>Gets the validation method.</summary>
      <returns>The name of the validation method.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.CustomValidationAttribute.ValidatorType">
      <summary>Gets the type that performs custom validation.</summary>
      <returns>The type that performs custom validation.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DataType">
      <summary>Represents an enumeration of the data types associated with data fields and parameters. </summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.CreditCard">
      <summary>Represents a credit card number.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Currency">
      <summary>Represents a currency value.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Custom">
      <summary>Represents a custom data type.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Date">
      <summary>Represents a date value.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.DateTime">
      <summary>Represents an instant in time, expressed as a date and time of day.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Duration">
      <summary>Represents a continuous time during which an object exists.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.EmailAddress">
      <summary>Represents an e-mail address.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Html">
      <summary>Represents an HTML file.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.ImageUrl">
      <summary>Represents a URL to an image.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.MultilineText">
      <summary>Represents multi-line text.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Password">
      <summary>Represent a password value.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.PhoneNumber">
      <summary>Represents a phone number value.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.PostalCode">
      <summary>Represents a postal code.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Text">
      <summary>Represents text that is displayed.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Time">
      <summary>Represents a time value.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Upload">
      <summary>Represents file upload data type.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.DataType.Url">
      <summary>Represents a URL value.</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DataTypeAttribute">
      <summary>Specifies the name of an additional type to associate with a data field.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DataTypeAttribute.#ctor(System.ComponentModel.DataAnnotations.DataType)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.DataTypeTypeAttribute" /> class by using the specified type name.</summary>
      <param name="dataType">The name of the type to associate with the data field.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DataTypeAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.DataTypeTypeAttribute" /> class by using the specified field template name.</summary>
      <param name="customDataType">The name of the custom field template to associate with the data field.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="customDataType" /> is null or an empty string (""). </exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DataTypeAttribute.CustomDataType">
      <summary>Gets the name of custom field template that is associated with the data field.</summary>
      <returns>The name of the custom field template that is associated with the data field.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DataTypeAttribute.DataType">
      <summary>Gets the type that is associated with the data field.</summary>
      <returns>One of the <see cref="T:System.ComponentModel.DataAnnotations.DataType" /> values.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DataTypeAttribute.DisplayFormat">
      <summary>Gets a data-field display format.</summary>
      <returns>The data-field display format.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DataTypeAttribute.GetDataTypeName">
      <summary>Returns the name of the type that is associated with the data field.</summary>
      <returns>The name of the type associated with the data field.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DataTypeAttribute.IsValid(System.Object)">
      <summary>Checks that the value of the data field is valid.</summary>
      <returns>true always.</returns>
      <param name="value">The data field value to validate.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DisplayAttribute">
      <summary>Provides a general-purpose attribute that lets you specify localizable strings for types and members of entity partial classes.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.DisplayAttribute" /> class.</summary>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateField">
      <summary>Gets or sets a value that indicates whether UI should be generated automatically in order to display this field.</summary>
      <returns>true if UI should be generated automatically to display this field; otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">An attempt was made to get the property value before it was set.</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateFilter">
      <summary>Gets or sets a value that indicates whether filtering UI is automatically displayed for this field. </summary>
      <returns>true if UI should be generated automatically to display filtering for this field; otherwise, false.</returns>
      <exception cref="T:System.InvalidOperationException">An attempt was made to get the property value before it was set.</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description">
      <summary>Gets or sets a value that is used to display a description in the UI.</summary>
      <returns>The value that is used to display a description in the UI.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetAutoGenerateField">
      <summary>Returns the value of the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateField" /> property.</summary>
      <returns>The value of <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateField" /> if the property has been initialized; otherwise, null.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetAutoGenerateFilter">
      <summary>Returns a value that indicates whether UI should be generated automatically in order to display filtering for this field. </summary>
      <returns>The value of <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.AutoGenerateFilter" /> if the property has been initialized; otherwise, null.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetDescription">
      <summary>Returns the value of the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> property.</summary>
      <returns>The localized description, if the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> has been specified and the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> property represents a resource key; otherwise, the non-localized value of the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> property.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> property and the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> property are initialized, but a public static property that has a name that matches the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> value could not be found for the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> property.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetGroupName">
      <summary>Returns the value of the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.GroupName" /> property.</summary>
      <returns>A value that will be used for grouping fields in the UI, if <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.GroupName" /> has been initialized; otherwise, null. If the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> property has been specified and the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.GroupName" /> property represents a resource key, a localized string is returned; otherwise, a non-localized string is returned.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetName">
      <summary>Returns a value that is used for field display in the UI.</summary>
      <returns>The localized string for the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" /> property, if the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> property has been specified and the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" /> property represents a resource key; otherwise, the non-localized value of the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" /> property.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> property and the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" /> property are initialized, but a public static property that has a name that matches the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" /> value could not be found for the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> property.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetOrder">
      <summary>Returns the value of the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Order" /> property.</summary>
      <returns>The value of the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Order" /> property, if it has been set; otherwise, null.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetPrompt">
      <summary>Returns the value of the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" /> property.</summary>
      <returns>Gets the localized string for the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" /> property if the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> property has been specified and if the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" /> property represents a resource key; otherwise, the non-localized value of the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" /> property.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayAttribute.GetShortName">
      <summary>Returns the value of the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" /> property.</summary>
      <returns>The localized string for the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" /> property if the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType" /> property has been specified and if the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" /> property represents a resource key; otherwise, the non-localized value of the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" /> value property.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.GroupName">
      <summary>Gets or sets a value that is used to group fields in the UI.</summary>
      <returns>A value that is used to group fields in the UI.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name">
      <summary>Gets or sets a value that is used for display in the UI.</summary>
      <returns>A value that is used for display in the UI.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Order">
      <summary>Gets or sets the order weight of the column.</summary>
      <returns>The order weight of the column.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt">
      <summary>Gets or sets a value that will be used to set the watermark for prompts in the UI.</summary>
      <returns>A value that will be used to display a watermark in the UI.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ResourceType">
      <summary>Gets or sets the type that contains the resources for the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" />, <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" />, <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" />, and <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> properties.</summary>
      <returns>The type of the resource that contains the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName" />, <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name" />, <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Prompt" />, and <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description" /> properties.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayAttribute.ShortName">
      <summary>Gets or sets a value that is used for the grid column label.</summary>
      <returns>A value that is for the grid column label.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DisplayColumnAttribute">
      <summary>Specifies the column that is displayed in the referred table as a foreign-key column.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.DisplayColumnAttribute" /> class by using the specified column. </summary>
      <param name="displayColumn">The name of the column to use as the display column.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.DisplayColumnAttribute" /> class by using the specified display and sort columns. </summary>
      <param name="displayColumn">The name of the column to use as the display column.</param>
      <param name="sortColumn">The name of the column to use for sorting.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.#ctor(System.String,System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.DisplayColumnAttribute" /> class by using the specified display column, and the specified sort column and sort order. </summary>
      <param name="displayColumn">The name of the column to use as the display column.</param>
      <param name="sortColumn">The name of the column to use for sorting.</param>
      <param name="sortDescending">true to sort in descending order; otherwise, false. The default is false.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.DisplayColumn">
      <summary>Gets the name of the column to use as the display field.</summary>
      <returns>The name of the display column.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.SortColumn">
      <summary>Gets the name of the column to use for sorting.</summary>
      <returns>The name of the sort column.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayColumnAttribute.SortDescending">
      <summary>Gets a value that indicates whether to sort in descending or ascending order.</summary>
      <returns>true if the column will be sorted in descending order; otherwise, false.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.DisplayFormatAttribute">
      <summary>Specifies how data fields are displayed and formatted by ASP.NET Dynamic Data.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.DisplayFormatAttribute" /> class. </summary>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.ApplyFormatInEditMode">
      <summary>Gets or sets a value that indicates whether the formatting string that is specified by the <see cref="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.DataFormatString" /> property is applied to the field value when the data field is in edit mode.</summary>
      <returns>true if the formatting string applies to the field value in edit mode; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.ConvertEmptyStringToNull">
      <summary>Gets or sets a value that indicates whether empty string values ("") are automatically converted to null when the data field is updated in the data source.</summary>
      <returns>true if empty string values are automatically converted to null; otherwise, false. The default is true.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.DataFormatString">
      <summary>Gets or sets the display format for the field value.</summary>
      <returns>A formatting string that specifies the display format for the value of the data field. The default is an empty string (""), which indicates that no special formatting is applied to the field value.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.HtmlEncode">
      <summary>Gets or sets a value that indicates whether the field should be HTML-encoded.</summary>
      <returns>true if the field should be HTML-encoded; otherwise, false.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.DisplayFormatAttribute.NullDisplayText">
      <summary>Gets or sets the text that is displayed for a field when the field's value is null.</summary>
      <returns>The text that is displayed for a field when the field's value is null. The default is an empty string (""), which indicates that this property is not set.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.EditableAttribute">
      <summary>Indicates whether a data field is editable.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EditableAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.EditableAttribute" /> class.</summary>
      <param name="allowEdit">true to specify that field is editable; otherwise, false.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.EditableAttribute.AllowEdit">
      <summary>Gets a value that indicates whether a field is editable.</summary>
      <returns>true if the field is editable; otherwise, false.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.EditableAttribute.AllowInitialValue">
      <summary>Gets or sets a value that indicates whether an initial value is enabled.</summary>
      <returns>true if an initial value is enabled; otherwise, false.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.EmailAddressAttribute">
      <summary>Validates an email address.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EmailAddressAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.EmailAddressAttribute" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EmailAddressAttribute.IsValid(System.Object)">
      <summary>Determines whether the specified value matches the pattern of a valid email address.</summary>
      <returns>true if the specified value is valid or null; otherwise, false.</returns>
      <param name="value">The value to validate.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute">
      <summary>Enables a .NET Framework enumeration to be mapped to a data column.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute" /> class.</summary>
      <param name="enumType">The type of the enumeration.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute.EnumType">
      <summary>Gets or sets the enumeration type.</summary>
      <returns>The enumeration type.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.EnumDataTypeAttribute.IsValid(System.Object)">
      <summary>Checks that the value of the data field is valid.</summary>
      <returns>true if the data field value is valid; otherwise, false.</returns>
      <param name="value">The data field value to validate.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.FileExtensionsAttribute">
      <summary>Validates file name extensions.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FileExtensionsAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.FileExtensionsAttribute" /> class.</summary>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.FileExtensionsAttribute.Extensions">
      <summary>Gets or sets the file name extensions.</summary>
      <returns>The file name extensions, or the default file extensions (".png", ".jpg", ".jpeg", and ".gif") if the property is not set.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FileExtensionsAttribute.FormatErrorMessage(System.String)">
      <summary>Applies formatting to an error message, based on the data field where the error occurred.</summary>
      <returns>The formatted error message.</returns>
      <param name="name">The name of the field that caused the validation failure.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FileExtensionsAttribute.IsValid(System.Object)">
      <summary>Checks that the specified file name extension or extensions is valid.</summary>
      <returns>true if the file name extension is valid; otherwise, false.</returns>
      <param name="value">A comma delimited list of valid file extensions.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.FilterUIHintAttribute">
      <summary>Represents an attribute that is used to specify the filtering behavior for a column.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.FilterUIHintAttribute" /> class by using the filter UI hint.</summary>
      <param name="filterUIHint">The name of the control to use for filtering.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.FilterUIHintAttribute" /> class by using the filter UI hint and presentation layer name.</summary>
      <param name="filterUIHint">The name of the control to use for filtering.</param>
      <param name="presentationLayer">The name of the presentation layer that supports this control.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.#ctor(System.String,System.String,System.Object[])">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.FilterUIHintAttribute" /> class by using the filter UI hint, presentation layer name, and control parameters.</summary>
      <param name="filterUIHint">The name of the control to use for filtering.</param>
      <param name="presentationLayer">The name of the presentation layer that supports this control.</param>
      <param name="controlParameters">The list of parameters for the control.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.ControlParameters">
      <summary>Gets the name/value pairs that are used as parameters in the control's constructor.</summary>
      <returns>The name/value pairs that are used as parameters in the control's constructor.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.Equals(System.Object)">
      <summary>Returns a value that indicates whether this attribute instance is equal to a specified object.</summary>
      <returns>True if the passed object is equal to this attribute instance; otherwise, false.</returns>
      <param name="obj">The object to compare with this attribute instance.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.FilterUIHint">
      <summary>Gets the name of the control to use for filtering.</summary>
      <returns>The name of the control to use for filtering.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.GetHashCode">
      <summary>Returns the hash code for this attribute instance.</summary>
      <returns>This attribute insatnce hash code.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.FilterUIHintAttribute.PresentationLayer">
      <summary>Gets the name of the presentation layer that supports this control.</summary>
      <returns>The name of the presentation layer that supports this control.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.IValidatableObject">
      <summary>Provides a way for an object to be invalidated.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.IValidatableObject.Validate(System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>Determines whether the specified object is valid.</summary>
      <returns>A collection that holds failed-validation information.</returns>
      <param name="validationContext">The validation context.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.KeyAttribute">
      <summary>Denotes one or more properties that uniquely identify an entity.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.KeyAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.KeyAttribute" /> class.</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.MaxLengthAttribute">
      <summary>Specifies the maximum length of array or string data allowed in a property.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MaxLengthAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.MaxLengthAttribute" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MaxLengthAttribute.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.MaxLengthAttribute" /> class based on the <paramref name="length" /> parameter.</summary>
      <param name="length">The maximum allowable length of array or string data.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MaxLengthAttribute.FormatErrorMessage(System.String)">
      <summary>Applies formatting to a specified error message.</summary>
      <returns>A localized string to describe the maximum acceptable length.</returns>
      <param name="name">The name to include in the formatted string.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MaxLengthAttribute.IsValid(System.Object)">
      <summary>Determines whether a specified object is valid.</summary>
      <returns>true if the value is null, or if the value is less than or equal to the specified maximum length; otherwise, false.</returns>
      <param name="value">The object to validate.</param>
      <exception cref="Sytem.InvalidOperationException">Length is zero or less than negative one.</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.MaxLengthAttribute.Length">
      <summary>Gets the maximum allowable length of the array or string data.</summary>
      <returns>The maximum allowable length of the array or string data.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.MinLengthAttribute">
      <summary>Specifies the minimum length of array or string data allowed in a property.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MinLengthAttribute.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.MinLengthAttribute" /> class.</summary>
      <param name="length">The length of the array or string data.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MinLengthAttribute.FormatErrorMessage(System.String)">
      <summary>Applies formatting to a specified error message.</summary>
      <returns>A localized string to describe the minimum acceptable length.</returns>
      <param name="name">The name to include in the formatted string.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.MinLengthAttribute.IsValid(System.Object)">
      <summary>Determines whether a specified object is valid.</summary>
      <returns>true if the specified object is valid; otherwise, false.</returns>
      <param name="value">The object to validate.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.MinLengthAttribute.Length">
      <summary>Gets or sets the minimum allowable length of the array or string data.</summary>
      <returns>The minimum allowable length of the array or string data.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.PhoneAttribute">
      <summary>Specifies that a data field value is a  well-formed phone number using a regular expression for phone numbers.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.PhoneAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.PhoneAttribute" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.PhoneAttribute.IsValid(System.Object)">
      <summary>Determines whether the specified phone number is in a valid phone number format. </summary>
      <returns>true if the phone number is valid; otherwise, false.</returns>
      <param name="value">The value to validate.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.RangeAttribute">
      <summary>Specifies the numeric range constraints for the value of a data field. </summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.#ctor(System.Double,System.Double)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.RangeAttribute" /> class by using the specified minimum and maximum values. </summary>
      <param name="minimum">Specifies the minimum value allowed for the data field value.</param>
      <param name="maximum">Specifies the maximum value allowed for the data field value.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.#ctor(System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.RangeAttribute" /> class by using the specified minimum and maximum values.</summary>
      <param name="minimum">Specifies the minimum value allowed for the data field value.</param>
      <param name="maximum">Specifies the maximum value allowed for the data field value.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.#ctor(System.Type,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.RangeAttribute" /> class by using the specified minimum and maximum values and the specific type.</summary>
      <param name="type">Specifies the type of the object to test.</param>
      <param name="minimum">Specifies the minimum value allowed for the data field value.</param>
      <param name="maximum">Specifies the maximum value allowed for the data field value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="type" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.FormatErrorMessage(System.String)">
      <summary>Formats the error message that is displayed when range validation fails.</summary>
      <returns>The formatted error message.</returns>
      <param name="name">The name of the field that caused the validation failure. </param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RangeAttribute.IsValid(System.Object)">
      <summary>Checks that the value of the data field is in the specified range.</summary>
      <returns>true if the specified value is in the range; otherwise, false.</returns>
      <param name="value">The data field value to validate.</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">The data field value was outside the allowed range.</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RangeAttribute.Maximum">
      <summary>Gets the maximum allowed field value.</summary>
      <returns>The maximum value that is allowed for the data field.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RangeAttribute.Minimum">
      <summary>Gets the minimum allowed field value.</summary>
      <returns>The minimu value that is allowed for the data field.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RangeAttribute.OperandType">
      <summary>Gets the type of the data field whose value must be validated.</summary>
      <returns>The type of the data field whose value must be validated.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.RegularExpressionAttribute">
      <summary>Specifies that a data field value in ASP.NET Dynamic Data must match the specified regular expression.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RegularExpressionAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.RegularExpressionAttribute" /> class.</summary>
      <param name="pattern">The regular expression that is used to validate the data field value. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="pattern" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RegularExpressionAttribute.FormatErrorMessage(System.String)">
      <summary>Formats the error message to display if the regular expression validation fails.</summary>
      <returns>The formatted error message.</returns>
      <param name="name">The name of the field that caused the validation failure.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RegularExpressionAttribute.IsValid(System.Object)">
      <summary>Checks whether the value entered by the user matches the regular expression pattern. </summary>
      <returns>true if validation is successful; otherwise, false.</returns>
      <param name="value">The data field value to validate.</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">The data field value did not match the regular expression pattern.</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RegularExpressionAttribute.Pattern">
      <summary>Gets the regular expression pattern.</summary>
      <returns>The pattern to match.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.RequiredAttribute">
      <summary>Specifies that a data field value is required.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RequiredAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.RequiredAttribute" /> class.</summary>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.RequiredAttribute.AllowEmptyStrings">
      <summary>Gets or sets a value that indicates whether an empty string is allowed.</summary>
      <returns>true if an empty string is allowed; otherwise, false. The default value is false.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.RequiredAttribute.IsValid(System.Object)">
      <summary>Checks that the value of the required data field is not empty.</summary>
      <returns>true if validation is successful; otherwise, false.</returns>
      <param name="value">The data field value to validate.</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">The data field value was null.</exception>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute">
      <summary>Specifies whether a class or data column uses scaffolding.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of <see cref="T:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute" /> using the <see cref="P:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute.Scaffold" /> property.</summary>
      <param name="scaffold">The value that specifies whether scaffolding is enabled.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ScaffoldColumnAttribute.Scaffold">
      <summary>Gets or sets the value that specifies whether scaffolding is enabled.</summary>
      <returns>true, if scaffolding is enabled; otherwise false.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.StringLengthAttribute">
      <summary>Specifies the minimum and maximum length of characters that are allowed in a data field.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.StringLengthAttribute.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.StringLengthAttribute" /> class by using a specified maximum length.</summary>
      <param name="maximumLength">The maximum length of a string. </param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.StringLengthAttribute.FormatErrorMessage(System.String)">
      <summary>Applies formatting to a specified error message.</summary>
      <returns>The formatted error message.</returns>
      <param name="name">The name of the field that caused the validation failure.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumLength" /> is negative. -or-<paramref name="maximumLength" /> is less than <paramref name="minimumLength" />.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.StringLengthAttribute.IsValid(System.Object)">
      <summary>Determines whether a specified object is valid.</summary>
      <returns>true if the specified object is valid; otherwise, false.</returns>
      <param name="value">The object to validate.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="maximumLength" /> is negative.-or-<paramref name="maximumLength" /> is less than <see cref="P:System.ComponentModel.DataAnnotations.StringLengthAttribute.MinimumLength" />.</exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.StringLengthAttribute.MaximumLength">
      <summary>Gets or sets the maximum length of a string.</summary>
      <returns>The maximum length a string. </returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.StringLengthAttribute.MinimumLength">
      <summary>Gets or sets the minimum length of a string.</summary>
      <returns>The minimum length of a string.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.TimestampAttribute">
      <summary>Specifies the data type of the column as a row version.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.TimestampAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.TimestampAttribute" /> class.</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.UIHintAttribute">
      <summary>Specifies the template or user control that Dynamic Data uses to display a data field. </summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.UIHintAttribute" /> class by using a specified user control. </summary>
      <param name="uiHint">The user control to use to display the data field. </param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.UIHintAttribute" /> class using the specified user control and specified presentation layer. </summary>
      <param name="uiHint">The user control (field template) to use to display the data field.</param>
      <param name="presentationLayer">The presentation layer that uses the class. Can be set to "HTML", "Silverlight", "WPF", or "WinForms".</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.#ctor(System.String,System.String,System.Object[])">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.UIHintAttribute" /> class by using the specified user control, presentation layer, and control parameters.</summary>
      <param name="uiHint">The user control (field template) to use to display the data field.</param>
      <param name="presentationLayer">The presentation layer that uses the class. Can be set to "HTML", "Silverlight", "WPF", or "WinForms".</param>
      <param name="controlParameters">The object to use to retrieve values from any data sources. </param>
      <exception cref="T:System.ArgumentException">
        <see cref="P:System.ComponentModel.DataAnnotations.UIHintAttribute.ControlParameters" /> is null or it is a constraint key.-or-The value of <see cref="P:System.ComponentModel.DataAnnotations.UIHintAttribute.ControlParameters" /> is not a string. </exception>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.UIHintAttribute.ControlParameters">
      <summary>Gets or sets the <see cref="T:System.Web.DynamicData.DynamicControlParameter" /> object to use to retrieve values from any data source.</summary>
      <returns>A collection of key/value pairs. </returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.Equals(System.Object)">
      <summary>Gets a value that indicates whether this instance is equal to the specified object.</summary>
      <returns>true if the specified object is equal to this instance; otherwise, false.</returns>
      <param name="obj">The object to compare with this instance, or a null reference.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UIHintAttribute.GetHashCode">
      <summary>Gets the hash code for the current instance of the attribute.</summary>
      <returns>The attribute instance hash code.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.UIHintAttribute.PresentationLayer">
      <summary>Gets or sets the presentation layer that uses the <see cref="T:System.ComponentModel.DataAnnotations.UIHintAttribute" /> class. </summary>
      <returns>The presentation layer that is used by this class.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.UIHintAttribute.UIHint">
      <summary>Gets or sets the name of the field template to use to display the data field.</summary>
      <returns>The name of the field template that displays the data field.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.UrlAttribute">
      <summary>Provides URL validation.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UrlAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.UrlAttribute" />class.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.UrlAttribute.IsValid(System.Object)">
      <summary>Validates the format of the specified URL.</summary>
      <returns>true if the URL format is valid or null; otherwise, false.</returns>
      <param name="value">The URL to validate.</param>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ValidationAttribute">
      <summary>Serves as the base class for all validation attributes.</summary>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">The <see cref="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceType" /> and <see cref="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceName" /> properties for localized error message are set at the same time that the non-localized <see cref="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessage" /> property error message is set.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.#ctor(System.Func{System.String})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> class by using the function that enables access to validation resources.</summary>
      <param name="errorMessageAccessor">The function that enables access to validation resources.</param>
      <exception cref="T:System:ArgumentNullException">
        <paramref name="errorMessageAccessor" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> class by using the error message to associate with a validation control.</summary>
      <param name="errorMessage">The error message to associate with a validation control.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessage">
      <summary>Gets or sets an error message to associate with a validation control if validation fails.</summary>
      <returns>The error message that is associated with the validation control.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceName">
      <summary>Gets or sets the error message resource name to use in order to look up the <see cref="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceType" /> property value if validation fails.</summary>
      <returns>The error message resource that is associated with a validation control.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageResourceType">
      <summary>Gets or sets the resource type to use for error-message lookup if validation fails.</summary>
      <returns>The type of error message that is associated with a validation control.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.ErrorMessageString">
      <summary>Gets the localized validation error message.</summary>
      <returns>The localized validation error message.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.FormatErrorMessage(System.String)">
      <summary>Applies formatting to an error message, based on the data field where the error occurred. </summary>
      <returns>An instance of the formatted error message.</returns>
      <param name="name">The name to include in the formatted message.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.GetValidationResult(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>Checks whether the specified value is valid with respect to the current validation attribute.</summary>
      <returns>An instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> class. </returns>
      <param name="value">The value to validate.</param>
      <param name="validationContext">The context information about the validation operation.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.IsValid(System.Object)">
      <summary>Determines whether the specified value of the object is valid. </summary>
      <returns>true if the specified value is valid; otherwise, false.</returns>
      <param name="value">The value of the object to validate. </param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.IsValid(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>Validates the specified value with respect to the current validation attribute.</summary>
      <returns>An instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> class. </returns>
      <param name="value">The value to validate.</param>
      <param name="validationContext">The context information about the validation operation.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationAttribute.RequiresValidationContext">
      <summary>Gets a value that indicates whether the attribute requires validation context.</summary>
      <returns>true if the attribute requires validation context; otherwise, false.</returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.Validate(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>Validates the specified object.</summary>
      <param name="value">The object to validate.</param>
      <param name="validationContext">The <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> object that describes the context where the validation checks are performed. This parameter cannot be null.</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">Validation failed.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationAttribute.Validate(System.Object,System.String)">
      <summary>Validates the specified object.</summary>
      <param name="value">The value of the object to validate.</param>
      <param name="name">The name to include in the error message.</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">
        <paramref name="value" /> is not valid.</exception>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ValidationContext">
      <summary>Describes the context in which a validation check is performed.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.#ctor(System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> class using the specified object instance</summary>
      <param name="instance">The object instance to validate. It cannot be null.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.#ctor(System.Object,System.Collections.Generic.IDictionary{System.Object,System.Object})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> class using the specified object and an optional property bag.</summary>
      <param name="instance">The object instance to validate.  It cannot be null</param>
      <param name="items">An optional set of key/value pairs to make available to consumers.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.#ctor(System.Object,System.IServiceProvider,System.Collections.Generic.IDictionary{System.Object,System.Object})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> class using the service provider and dictionary of service consumers. </summary>
      <param name="instance">The object to validate. This parameter is required.</param>
      <param name="serviceProvider">The object that implements the <see cref="T:System.IServiceProvider" /> interface. This parameter is optional.</param>
      <param name="items">A dictionary of key/value pairs to make available to the service consumers. This parameter is optional.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.DisplayName">
      <summary>Gets or sets the name of the member to validate. </summary>
      <returns>The name of the member to validate. </returns>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.GetService(System.Type)">
      <summary>Returns the service that provides custom validation.</summary>
      <returns>An instance of the service, or null if the service is not available.</returns>
      <param name="serviceType">The type of the service to use for validation.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationContext.InitializeServiceProvider(System.Func{System.Type,System.Object})">
      <summary>Initializes the <see cref="T:System.ComponentModel.DataAnnotations.ValidationContext" /> using a service provider that can return service instances by type when GetService is called.</summary>
      <param name="serviceProvider">The service provider.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.Items">
      <summary>Gets the dictionary of key/value pairs that is associated with this context.</summary>
      <returns>The dictionary of the key/value pairs for this context.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.MemberName">
      <summary>Gets or sets the name of the member to validate. </summary>
      <returns>The name of the member to validate. </returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.ObjectInstance">
      <summary>Gets the object to validate.</summary>
      <returns>The object to validate.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationContext.ObjectType">
      <summary>Gets the type of the object to validate.</summary>
      <returns>The type of the object to validate.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ValidationException">
      <summary>Represents the exception that occurs during validation of a data field when the <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> class is used. </summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> class using an error message generated by the system.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor(System.ComponentModel.DataAnnotations.ValidationResult,System.ComponentModel.DataAnnotations.ValidationAttribute,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> class by using a validation result, a validation attribute, and the value of the current exception.</summary>
      <param name="validationResult">The list of validation results.</param>
      <param name="validatingAttribute">The attribute that caused the current exception.</param>
      <param name="value">The value of the object that caused the attribute to trigger the validation error.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> class using a specified error message.</summary>
      <param name="message">A specified message that states the error.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor(System.String,System.ComponentModel.DataAnnotations.ValidationAttribute,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> class using a specified error message, a validation attribute, and the value of the current exception.</summary>
      <param name="errorMessage">The message that states the error.</param>
      <param name="validatingAttribute">The attribute that caused the current exception.</param>
      <param name="value">The value of the object that caused the attribute to trigger validation error.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationException" /> class using a specified error message and a collection of inner exception instances.</summary>
      <param name="message">The error message. </param>
      <param name="innerException">The collection of validation exceptions.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationException.ValidationAttribute">
      <summary>Gets the instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> class that triggered this exception.</summary>
      <returns>An instance of the validation attribute type that triggered this exception.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationException.ValidationResult">
      <summary>Gets the <see cref="P:System.ComponentModel.DataAnnotations.ValidationException.ValidationResult" /> instance that describes the validation error.</summary>
      <returns>The <see cref="P:System.ComponentModel.DataAnnotations.ValidationException.ValidationResult" /> instance that describes the validation error.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationException.Value">
      <summary>Gets the value of the object that causes the <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> class to trigger this exception.</summary>
      <returns>The value of the object that caused the <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> class to trigger the validation error.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.ValidationResult">
      <summary>Represents a container for the results of a validation request.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationResult.#ctor(System.ComponentModel.DataAnnotations.ValidationResult)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> class by using a <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> object.</summary>
      <param name="validationResult">The validation result object.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationResult.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> class by using an error message.</summary>
      <param name="errorMessage">The error message.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationResult.#ctor(System.String,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.ValidationResult" /> class by using an error message and a list of members that have validation errors.</summary>
      <param name="errorMessage">The error message.</param>
      <param name="memberNames">The list of member names that have validation errors.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationResult.ErrorMessage">
      <summary>Gets the error message for the validation.</summary>
      <returns>The error message for the validation.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.ValidationResult.MemberNames">
      <summary>Gets the collection of member names that indicate which fields have validation errors.</summary>
      <returns>The collection of member names that indicate which fields have validation errors.</returns>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.ValidationResult.Success">
      <summary>Represents the success of the validation (true if validation was successful; otherwise, false).</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.ValidationResult.ToString">
      <summary>Returns a string representation of the current validation result.</summary>
      <returns>The current validation result.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Validator">
      <summary>Defines a helper class that can be used to validate objects, properties, and methods when it is included in their associated <see cref="T:System.ComponentModel.DataAnnotations.ValidationAttribute" /> attributes.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.TryValidateObject(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.ICollection{System.ComponentModel.DataAnnotations.ValidationResult})">
      <summary>Determines whether the specified object is valid using the validation context and validation results collection.</summary>
      <returns>true if the object validates; otherwise, false.</returns>
      <param name="instance">The object to validate.</param>
      <param name="validationContext">The context that describes the object to validate.</param>
      <param name="validationResults">A collection to hold each failed validation.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.TryValidateObject(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.ICollection{System.ComponentModel.DataAnnotations.ValidationResult},System.Boolean)">
      <summary>Determines whether the specified object is valid using the validation context, validation results collection, and a value that specifies whether to validate all properties.</summary>
      <returns>true if the object validates; otherwise, false.</returns>
      <param name="instance">The object to validate.</param>
      <param name="validationContext">The context that describes the object to validate.</param>
      <param name="validationResults">A collection to hold each failed validation.</param>
      <param name="validateAllProperties">true to validate all properties; if false, only required attributes are validated..</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.TryValidateProperty(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.ICollection{System.ComponentModel.DataAnnotations.ValidationResult})">
      <summary>Validates the property.</summary>
      <returns>true if the property validates; otherwise, false.</returns>
      <param name="value">The value to validate.</param>
      <param name="validationContext">The context that describes the property to validate.</param>
      <param name="validationResults">A collection to hold each failed validation. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> cannot be assigned to the property.-or-<paramref name="value " />is null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.TryValidateValue(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.ICollection{System.ComponentModel.DataAnnotations.ValidationResult},System.Collections.Generic.IEnumerable{System.ComponentModel.DataAnnotations.ValidationAttribute})">
      <summary>Returns a value that indicates whether the specified value is valid with the specified attributes.</summary>
      <returns>true if the object validates; otherwise, false.</returns>
      <param name="value">The value to validate.</param>
      <param name="validationContext">The context that describes the object to validate.</param>
      <param name="validationResults">A collection to hold failed validations. </param>
      <param name="validationAttributes">The validation attributes.</param>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.ValidateObject(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>Determines whether the specified object is valid using the validation context.</summary>
      <param name="instance">The object to validate.</param>
      <param name="validationContext">The context that describes the object to validate.</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">The object is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.ValidateObject(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Boolean)">
      <summary>Determines whether the specified object is valid using the validation context, and a value that specifies whether to validate all properties.</summary>
      <param name="instance">The object to validate.</param>
      <param name="validationContext">The context that describes the object to validate.</param>
      <param name="validateAllProperties">true to validate all properties; otherwise, false.</param>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">
        <paramref name="instance" /> is not valid.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="instance" /> is null.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.ValidateProperty(System.Object,System.ComponentModel.DataAnnotations.ValidationContext)">
      <summary>Validates the property.</summary>
      <param name="value">The value to validate.</param>
      <param name="validationContext">The context that describes the property to validate.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> cannot be assigned to the property.</exception>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">The <paramref name="value" /> parameter is not valid.</exception>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Validator.ValidateValue(System.Object,System.ComponentModel.DataAnnotations.ValidationContext,System.Collections.Generic.IEnumerable{System.ComponentModel.DataAnnotations.ValidationAttribute})">
      <summary>Validates the specified attributes.</summary>
      <param name="value">The value to validate.</param>
      <param name="validationContext">The context that describes the object to validate.</param>
      <param name="validationAttributes">The validation attributes.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="validationContext" /> parameter is null.</exception>
      <exception cref="T:System.ComponentModel.DataAnnotations.ValidationException">The <paramref name="value" /> parameter does not validate with the <paramref name="validationAttributes" /> parameter.</exception>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute">
      <summary>Represents the database column that a property is mapped to.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute" /> class.</summary>
      <param name="name">The name of the column the property is mapped to.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.Name">
      <summary>Gets the name of the column the property is mapped to.</summary>
      <returns>The name of the column the property is mapped to.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.Order">
      <summary>Gets or sets the zero-based order of the column the property is mapped to.</summary>
      <returns>The order of the column.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.ColumnAttribute.TypeName">
      <summary>Gets or sets the database provider specific data type of the column the property is mapped to.</summary>
      <returns>The database provider specific data type of the column the property is mapped to.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.ComplexTypeAttribute">
      <summary>Denotes that the class is a complex type. Complex types are non-scalar properties of entity types that enable scalar properties to be organized within entities. Complex types do not have keys and cannot be managed by the Entity Framework apart from the parent object.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.ComplexTypeAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.Schema.ComplexTypeAttribute" /> class.</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedAttribute">
      <summary>Specifies how the database generates values for a property.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedAttribute.#ctor(System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedAttribute" /> class.</summary>
      <param name="databaseGeneratedOption">The database generated option.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedAttribute.DatabaseGeneratedOption">
      <summary>Gets or sets the pattern used to generate values for the property in the database.</summary>
      <returns>The database generated option.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption">
      <summary>Represents the pattern used to generate values for a property in the database.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Computed">
      <summary>The database generates a value when a row is inserted or updated.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.Identity">
      <summary>The database generates a value when a row is inserted.</summary>
    </member>
    <member name="F:System.ComponentModel.DataAnnotations.Schema.DatabaseGeneratedOption.None">
      <summary>The database does not generate values.</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute">
      <summary>Denotes a property used as a foreign key in a relationship. The annotation may be placed on the foreign key property and specify the associated navigation property name, or placed on a navigation property and specify the associated foreign key name.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute" /> class.</summary>
      <param name="name">If you add the ForeigKey attribute to a foreign key property, you should specify the name of the associated navigation property. If you add the ForeigKey attribute to a navigation property, you should specify the name of the associated foreign key(s). If a navigation property has multiple foreign keys, use comma to separate the list of foreign key names. For more information, see Code First Data Annotations.  </param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute.Name">
      <summary>If you add the ForeigKey attribute to a foreign key property, you should specify the name of the associated navigation property. If you add the ForeigKey attribute to a navigation property, you should specify the name of the associated foreign key(s). If a navigation property has multiple foreign keys, use comma to separate the list of foreign key names. For more information, see Code First Data Annotations.  </summary>
      <returns>The name of the associated navigation property or the associated foreign key property.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute">
      <summary>Specifies the inverse of a navigation property that represents the other end of the same relationship.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute" /> class using the specified property.</summary>
      <param name="property">The navigation property representing the other end of the same relationship.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute.Property">
      <summary>Gets the navigation property representing the other end of the same relationship.</summary>
      <returns>The property of the attribute.</returns>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.NotMappedAttribute">
      <summary>Denotes that a property or class should be excluded from database mapping.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.NotMappedAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.Schema.NotMappedAttribute" /> class.</summary>
    </member>
    <member name="T:System.ComponentModel.DataAnnotations.Schema.TableAttribute">
      <summary>Specifies the database table that a class is mapped to.</summary>
    </member>
    <member name="M:System.ComponentModel.DataAnnotations.Schema.TableAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.DataAnnotations.Schema.TableAttribute" /> class using the specified name of the table.</summary>
      <param name="name">The name of the table the class is mapped to.</param>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.TableAttribute.Name">
      <summary>Gets the name of the table the class is mapped to.</summary>
      <returns>The name of the table the class is mapped to.</returns>
    </member>
    <member name="P:System.ComponentModel.DataAnnotations.Schema.TableAttribute.Schema">
      <summary>Gets or sets the schema of the table the class is mapped to.</summary>
      <returns>The schema of the table the class is mapped to.</returns>
    </member>
  </members>
</doc>