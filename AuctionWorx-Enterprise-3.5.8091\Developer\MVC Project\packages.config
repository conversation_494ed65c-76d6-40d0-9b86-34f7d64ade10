﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="CsvHelper" version="30.0.1" targetFramework="net48" />
  <package id="DuoVia.FuzzyStrings" version="2.1.0" targetFramework="net48" />
  <package id="EasyPost-Official" version="5.1.0" targetFramework="net48" />
  <package id="EPPlus" version="8.1.1" targetFramework="net48" />
  <package id="EPPlus.Interfaces" version="8.1.0" targetFramework="net48" />
  <package id="Google.Apis" version="1.68.0" targetFramework="net48" />
  <package id="Google.Apis.Auth" version="1.68.0" targetFramework="net48" />
  <package id="Google.Apis.Core" version="1.68.0" targetFramework="net48" />
  <package id="Google.Apis.ShoppingContent.v2_1" version="1.64.0.3258" targetFramework="net48" />
  <package id="Microsoft.AspNet.Mvc" version="5.3.0" targetFramework="net48" />
  <package id="Microsoft.AspNet.Razor" version="3.3.0" targetFramework="net48" />
  <package id="Microsoft.AspNet.WebPages" version="3.3.0" targetFramework="net48" />
  <package id="Microsoft.Azure.KeyVault.Core" version="3.0.5" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.HashCode" version="1.0.0" targetFramework="net48" />
  <package id="Microsoft.CSharp" version="4.3.0" targetFramework="net48" />
  <package id="Microsoft.Data.Edm" version="5.8.5" targetFramework="net48" />
  <package id="Microsoft.Data.OData" version="5.8.5" targetFramework="net48" />
  <package id="Microsoft.Data.Services.Client" version="5.8.5" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="2.1.1" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Abstractions" version="6.36.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="6.36.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Logging" version="6.36.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Tokens" version="6.36.0" targetFramework="net48" />
  <package id="Microsoft.IO.RecyclableMemoryStream" version="3.0.1" targetFramework="net48" />
  <package id="Microsoft.Rest.ClientRuntime" version="2.3.24" targetFramework="net48" />
  <package id="Microsoft.Rest.ClientRuntime.Azure" version="3.3.18" targetFramework="net48" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.CodeDom" version="7.0.0" targetFramework="net48" />
  <package id="System.ComponentModel.Annotations" version="5.0.0" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.7.1" targetFramework="net48" />
  <package id="System.IdentityModel.Tokens.Jwt" version="6.36.0" targetFramework="net48" />
  <package id="System.Management" version="7.0.2" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Net.WebSockets.Client.Managed" version="1.0.22" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Reactive" version="6.0.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Xml" version="8.0.2" targetFramework="net48" />
  <package id="System.Spatial" version="5.8.5" targetFramework="net48" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="8.0.0" targetFramework="net48" />
  <package id="System.Text.Json" version="8.0.5" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
  <package id="Twilio" version="6.18.0" targetFramework="net48" />
  <package id="WindowsAzure.Storage" version="8.6.0" targetFramework="net48" />
</packages>